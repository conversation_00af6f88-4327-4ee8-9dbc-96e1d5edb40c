#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MainAgent集成
验证用户行为模拟器与MainAgent的集成是否正确
"""

import os
from taxi_agent_system import MainAgent
from user_behavior_simulator import UserBehaviorSimulator


def test_main_agent_direct():
    """直接测试MainAgent"""
    print("=== 直接测试MainAgent ===")
    
    try:
        agent = MainAgent()
        response = agent.process_user_input("我要从康德大厦打车到太阳宫")
        print(f"✅ MainAgent直接调用成功")
        print(f"输入: 我要从康德大厦打车到太阳宫")
        print(f"输出: {response}")
        return True
    except Exception as e:
        print(f"❌ MainAgent直接调用失败: {e}")
        return False


def test_simulator_with_main_agent():
    """测试模拟器与MainAgent的集成"""
    print("\n=== 测试模拟器与MainAgent集成 ===")
    
    try:
        # 初始化模拟器
        simulator = UserBehaviorSimulator()
        print("✅ 模拟器初始化成功")
        
        # 检查taxi_agent是否是MainAgent实例
        if isinstance(simulator.taxi_agent, MainAgent):
            print("✅ taxi_agent是MainAgent实例")
        else:
            print(f"❌ taxi_agent类型错误: {type(simulator.taxi_agent)}")
            return False
        
        # 创建会话并生成用户输入
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, 2)  # 有历史记录用户
        
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"✅ 用户输入生成: {user_input}")
        
        # 通过模拟器调用MainAgent
        system_response = simulator.taxi_agent.process_user_input(user_input)
        print(f"✅ 系统回复: {system_response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_conversation_flow():
    """测试完整对话流程"""
    print("\n=== 测试完整对话流程 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        # 测试不同场景
        test_cases = [
            (1, "首次使用用户"),
            (2, "有历史记录用户"),
            (3, "探索型用户")
        ]
        
        for scenario_type, scenario_name in test_cases:
            print(f"\n测试场景: {scenario_name}")
            
            session_id = simulator.generate_session_id()
            simulator.assign_scenario_to_session(session_id, scenario_type)
            
            # 模拟2轮对话
            context = ""
            for turn in range(1, 3):
                user_input = simulator.generate_user_input(session_id, context, turn)
                system_response = simulator.taxi_agent.process_user_input(user_input)
                
                print(f"  第{turn}轮 - 用户: {user_input}")
                print(f"         系统: {system_response[:50]}...")
                
                context = f"用户说：{user_input}\n系统回复：{system_response}"
        
        print("✅ 对话流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        return False


def test_batch_simulation():
    """测试批量模拟"""
    print("\n=== 测试批量模拟 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        # 生成小规模测试场景
        test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=1)
        
        print(f"✅ 批量生成成功，共{len(test_scenarios)}个场景")
        
        # 检查每个场景的结果
        for scenario in test_scenarios:
            scenario_name = scenario['scenario_name']
            conversation = scenario['conversation']
            
            print(f"  {scenario_name}: {len(conversation)}轮对话")
            if conversation:
                first_turn = conversation[0]
                print(f"    首轮 - 用户: {first_turn['user_input']}")
                print(f"           系统: {first_turn['system_response'][:30]}...")
        
        # 分析结果
        analysis = simulator.analyze_test_results(test_scenarios)
        print(f"✅ 结果分析完成，成功率: {analysis['overall_stats']['success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量模拟测试失败: {e}")
        return False


def test_with_first_message_and_type(first_message: str, scenario_type: int):
    """
    以用户第一句话和类型作为入参的测试函数

    Args:
        first_message: 用户的第一句话
        scenario_type: 用户场景类型 (1-5)

    Returns:
        dict: 测试结果
    """
    print(f"=== 测试用户输入: '{first_message}' (场景类型: {scenario_type}) ===")

    try:
        # 初始化模拟器
        simulator = UserBehaviorSimulator()

        # 验证场景类型
        if scenario_type not in simulator.user_scenarios:
            return {
                "success": False,
                "error": f"无效的场景类型: {scenario_type}",
                "scenario_name": None,
                "user_input": first_message,
                "system_response": None
            }

        scenario_name = simulator.user_scenarios[scenario_type]['name']
        print(f"场景类型: {scenario_name}")

        # 创建会话并分配场景
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)

        # 直接使用提供的第一句话作为用户输入
        user_input = first_message
        print(f"用户输入: {user_input}")

        # 通过MainAgent获取系统回复
        system_response = simulator.taxi_agent.process_user_input(user_input)
        print(f"系统回复: {system_response}")

        # 分析回复质量
        response_analysis = analyze_response_quality(user_input, system_response, scenario_type)

        result = {
            "success": True,
            "scenario_type": scenario_type,
            "scenario_name": scenario_name,
            "user_input": user_input,
            "system_response": system_response,
            "response_analysis": response_analysis,
            "session_id": session_id
        }

        print(f"✅ 测试完成")
        return result

    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e),
            "scenario_type": scenario_type,
            "scenario_name": simulator.user_scenarios.get(scenario_type, {}).get('name', 'Unknown'),
            "user_input": first_message,
            "system_response": None
        }
        print(f"❌ 测试失败: {e}")
        return error_result


def analyze_response_quality(user_input: str, system_response: str, scenario_type: int) -> dict:
    """分析系统回复质量"""
    analysis = {
        "is_relevant": False,
        "is_helpful": False,
        "handles_scenario": False,
        "response_length": len(system_response),
        "keywords_found": []
    }

    # 检查回复是否相关
    if system_response and len(system_response.strip()) > 0:
        analysis["is_relevant"] = True

    # 根据场景类型检查回复质量
    scenario_keywords = {
        1: ["帮助", "引导", "需要", "提供", "信息"],  # 首次使用用户
        2: ["历史", "记录", "上次", "之前", "熟悉"],  # 有历史记录用户
        3: ["推荐", "附近", "周边", "探索", "发现"],  # 探索型用户
        4: ["聊天", "话题", "心情", "天气", "其他"],  # 随意聊天用户
        5: ["抱歉", "不支持", "范围", "建议", "其他"]   # 超出能力范围用户
    }

    keywords = scenario_keywords.get(scenario_type, [])
    found_keywords = [kw for kw in keywords if kw in system_response]
    analysis["keywords_found"] = found_keywords
    analysis["handles_scenario"] = len(found_keywords) > 0

    # 检查是否有帮助
    helpful_indicators = ["可以", "帮助", "为您", "建议", "推荐", "提供"]
    analysis["is_helpful"] = any(indicator in system_response for indicator in helpful_indicators)

    return analysis


def batch_test_with_messages(test_cases: list):
    """
    批量测试多个用户输入和场景类型组合

    Args:
        test_cases: 测试用例列表，每个元素为 (first_message, scenario_type)

    Returns:
        list: 所有测试结果
    """
    print("🚗 批量测试用户输入和场景类型")
    print("=" * 60)

    results = []

    for i, (first_message, scenario_type) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}/{len(test_cases)}:")
        result = test_with_first_message_and_type(first_message, scenario_type)
        results.append(result)
        print("-" * 40)

    # 汇总分析
    print("\n📊 批量测试结果汇总:")
    successful_tests = [r for r in results if r["success"]]
    print(f"  成功测试: {len(successful_tests)}/{len(results)}")
    print(f"  成功率: {len(successful_tests)/len(results)*100:.1f}%")

    # 按场景类型分析
    scenario_stats = {}
    for result in results:
        scenario_type = result["scenario_type"]
        if scenario_type not in scenario_stats:
            scenario_stats[scenario_type] = {"total": 0, "success": 0}
        scenario_stats[scenario_type]["total"] += 1
        if result["success"]:
            scenario_stats[scenario_type]["success"] += 1

    print("\n按场景类型统计:")
    simulator = UserBehaviorSimulator()
    for scenario_type, stats in scenario_stats.items():
        scenario_name = simulator.user_scenarios[scenario_type]["name"]
        success_rate = stats["success"] / stats["total"] * 100 if stats["total"] > 0 else 0
        print(f"  场景{scenario_type} ({scenario_name}): {stats['success']}/{stats['total']} ({success_rate:.1f}%)")

    return results


def main():
    """主测试函数"""
    print("🚗 MainAgent集成测试 - 支持自定义用户输入")
    print("=" * 60)

    # 检查环境
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  注意: 未设置BAILIAN_API_KEY，将使用回退模式")

    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  注意: 未设置AMAP_API_KEY，地图功能可能受限")

    print()

    # 1. 运行原有测试
    print("1️⃣  运行基础集成测试:")
    print("-" * 30)

    basic_tests = [
        ("MainAgent直接调用", test_main_agent_direct),
        ("模拟器集成", test_simulator_with_main_agent),
    ]

    basic_results = []
    for test_name, test_func in basic_tests:
        print(f"运行测试: {test_name}")
        result = test_func()
        basic_results.append((test_name, result))
        print()

    # 2. 测试自定义用户输入
    print("2️⃣  测试自定义用户输入:")
    print("-" * 30)

    # 预定义测试用例
    test_cases = [
        ("你好，我想打车", 1),  # 首次使用用户
        ("我要从康德大厦打车到太阳宫", 2),  # 有历史记录用户
        ("附近有什么好玩的地方吗？", 3),  # 探索型用户
        ("今天天气真好啊", 4),  # 随意聊天用户
        ("帮我订个酒店", 5),  # 超出能力范围用户
        ("我要去机场，怎么走？", 1),  # 首次使用用户
        ("还是去上次那个地方", 2),  # 有历史记录用户
    ]

    custom_results = batch_test_with_messages(test_cases)

    # 3. 汇总所有结果
    print("\n3️⃣  总体结果汇总:")
    print("-" * 30)

    # 基础测试结果
    basic_passed = sum(1 for _, result in basic_results if result)
    print(f"基础集成测试: {basic_passed}/{len(basic_results)} 通过")

    # 自定义输入测试结果
    custom_passed = sum(1 for result in custom_results if result["success"])
    print(f"自定义输入测试: {custom_passed}/{len(custom_results)} 通过")

    # 总体成功率
    total_tests = len(basic_results) + len(custom_results)
    total_passed = basic_passed + custom_passed
    overall_success_rate = total_passed / total_tests * 100 if total_tests > 0 else 0

    print(f"\n总体结果: {total_passed}/{total_tests} 测试通过 ({overall_success_rate:.1f}%)")

    if total_passed == total_tests:
        print("🎉 所有测试通过！MainAgent集成成功。")
        print("\n📝 使用示例:")
        print("# 测试单个用户输入")
        print("result = test_with_first_message_and_type('我要打车', 1)")
        print("\n# 批量测试")
        print("results = batch_test_with_messages([('你好', 1), ('我要去机场', 2)])")
    else:
        print("⚠️  部分测试失败，请检查配置。")

    return total_passed == total_tests


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
