"""
高德地图API配置文件
"""

import os
from typing import Dict, Any

class AmapConfig:
    """高德地图配置类"""
    
    def __init__(self):
        self.api_key = os.getenv("AMAP_API_KEY")
        self.base_url = "https://restapi.amap.com/v3"
        
        # API端点配置
        self.endpoints = {
            "geocode": "/geocode/geo",           # 地理编码
            "regeocode": "/geocode/regeo",       # 逆地理编码
            "district": "/config/district",       # 行政区域查询
            "poi_search": "/place/text",         # POI搜索
            "poi_around": "/place/around",       # 周边搜索
            "route_driving": "/direction/driving", # 驾车路径规划
            "route_walking": "/direction/walking", # 步行路径规划
        }
        
        # 请求配置
        self.request_config = {
            "timeout": 10,
            "retries": 3,
            "headers": {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        }
        
        # POI类型映射
        self.poi_types = {
            "餐饮": "050000",
            "购物": "060000", 
            "生活服务": "070000",
            "体育休闲": "080000",
            "医疗保健": "090000",
            "住宿服务": "100000",
            "风景名胜": "110000",
            "商务住宅": "120000",
            "政府机构": "130000",
            "科教文化": "140000",
            "交通设施": "150000",
            "金融保险": "160000",
            "公司企业": "170000",
            "道路附属": "180000",
            "地名地址": "190000",
            "公共设施": "200000"
        }
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置"""
        issues = []
        
        if not self.api_key:
            issues.append("缺少高德地图API密钥，请设置环境变量 AMAP_API_KEY")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues
        }
    
    def get_poi_type_code(self, type_name: str) -> str:
        """获取POI类型代码"""
        return self.poi_types.get(type_name, "")


# 全局配置实例
amap_config = AmapConfig()


def setup_environment():
    """设置环境变量示例"""
    print("请设置以下环境变量：")
    print("export AMAP_API_KEY='your_amap_api_key_here'")
    print("export BAILIAN_API_KEY='your_bailian_api_key_here'")
    print("\n或者在Python中设置：")
    print("import os")
    print("os.environ['AMAP_API_KEY'] = 'your_amap_api_key_here'")
    print("os.environ['BAILIAN_API_KEY'] = 'your_bailian_api_key_here'")


if __name__ == "__main__":
    config = AmapConfig()
    validation = config.validate_config()
    
    if validation["valid"]:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败：")
        for issue in validation["issues"]:
            print(f"  - {issue}")
        print("\n")
        setup_environment()
