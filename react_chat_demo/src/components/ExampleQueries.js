import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { MessageSquare, MapPin, Building, Car, Plane } from 'lucide-react';

const QueriesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
`;

const QueryButton = styled(motion.button)`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
`;

const QueryIcon = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  flex-shrink: 0;
`;

const QueryText = styled.span`
  flex: 1;
  line-height: 1.4;
`;

const getIconForQuery = (query) => {
  const queryLower = query.toLowerCase();
  
  if (queryLower.includes('西湖') || queryLower.includes('在哪里')) {
    return <MapPin size={16} />;
  }
  if (queryLower.includes('城市代码') || queryLower.includes('杭州')) {
    return <Building size={16} />;
  }
  if (queryLower.includes('星巴克') || queryLower.includes('北京')) {
    return <Building size={16} />;
  }
  if (queryLower.includes('打车') || queryLower.includes('康德大厦')) {
    return <Car size={16} />;
  }
  if (queryLower.includes('机场') || queryLower.includes('首都机场')) {
    return <Plane size={16} />;
  }
  
  return <MessageSquare size={16} />;
};

const ExampleQueries = ({ queries, onQueryClick }) => {
  return (
    <QueriesContainer>
      {queries.map((query, index) => (
        <QueryButton
          key={index}
          onClick={() => onQueryClick(query)}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <QueryIcon>
            {getIconForQuery(query)}
          </QueryIcon>
          <QueryText>{query}</QueryText>
        </QueryButton>
      ))}
    </QueriesContainer>
  );
};

export default ExampleQueries;
