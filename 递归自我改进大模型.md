# 基于大模型的纯 LLM 驱动打车智能体技术框架

## 1. 总体思路

- **核心原则**：无需传统组件（NLU、Context Manager、Debug Agent、Mock 函数等），全部依赖大模型（如 GPT-4o）强大的上下文理解、推理和函数调用能力。
- **对话架构**：以 **Prompt Chain** 方式组织，按照「理解意图→缺失检测→函数调用→结果解释」五步在同一个 LLM 会话中完成。

```
用户 → LLM Prompt 聊天会话 ←→ LLM
                │
                ├─ 函数调用参数构建
                ├─ 调用外部打车 API (function calling)
                └─ 结果解析 & 响应文本生成
```

## 2. Prompt Chain 设计

### 2.1 统一 System 提示

```text
你是一个打车助手，能在一个对话中分别完成：
1. 理解用户意图和已提供的上下文槽位；
2. 检测缺失或模糊的信息并生成简洁的反问；
3. 构建符合 OpenAI function calling 规范的函数名和参数；
4. 解释函数返回结果并生成自然语言回复。
只使用一次 LLM 调用，每轮通过内部逻辑判断执行上述步骤。
```

### 2.2 用户 Prompt 模板

```text
用户: {user_input}
系统需完成：
- 已有信息：{conv_history_slots}
- 你要做：意图解析→缺失检测→生成函数调用 JSON→解释返回。

输出遵循 JSON 包装：
```

{{ "action": "call\_function" / "ask\_user", "name": "function\_name or null", "arguments": {{ ... }}, "follow\_up\_question": "如果action是ask\_user时填写，否则空字符串" }}

```
```

### 2.3 LLM 负责的能力


1. **函数调用**：借助 OpenAI 函数调用接口，LLM 直接生成 `{name, arguments}`，无需额外代码路由。
2. **Debug/抗噪**：模型在生成函数参数前，如检测到用户输入与历史冲突（模糊地址、相互矛盾），直接在 `follow_up_question` 中给出澄清。

## 3. 函数定义（Function Calling）

```jsonc
[
  {
    "name": "request_ride",
    "description": "向打车平台请求叫车",
    "parameters": {
      "type": "object",
      "properties": {
        "start_place": { "type": "string" },
        "end_place": { "type": "string" },
        "car_type": { "type": "string" }
      },
      "required": ["start_place","end_place"]
    }
  },
  {
    "name": "get_estimate",
    "description": "获取价格和距离预估",
    "parameters": {
      "type":"object",
      "properties": {
        "start_place": {"type":"string"},
        "end_place": {"type":"string"}
      },
      "required":["start_place","end_place"]
    }
  }
]
```

## 4. 对话示例流程

1. **用户**：我要从王府井去国贸。
2. **Prompt** 将 `start_place=王府井`、`end_place=国贸` 填入 `conv_history_slots`，并指示 LLM 检测完备性。
3. **LLM 输出**：调用 `get_estimate` 函数；无缺失槽位。
4. **后端** 执行 `get_estimate`，返回 `{distance: 5.2, price: 23}`。
5. **LLM 二次生成**：解释：“国贸距离王府井约5.2公里，预估价格23元，是否继续下单？”并保持槽位。
6. **用户**：好的，帮我下单。
7. **LLM**：调用 `request_ride`，参数填全；并无反问。
8. **后端** 返回订单信息；LLM 解释并结束对话。

## 5. 提升效率的关键

- **Prompt 模板化**：所有对话轮次复用同一系统+用户 prompt，减少多次模型切换和自定义组件维护。
- **依赖 LLM 上下文窗口**：充分利用 GPT-4o 32K 令牌，将对话上下文与槽位一起传入，避免外部状态管理。
- **最少工具定义**：只定义2-3个精简函数，减少函数调用成本与复杂度。
- **链式交互**：将意图识别、缺失检测、参数构建、结果解释串联在单次对话中，尽量利用一次模型调用完成更多逻辑。

## 6. 单元测试思路

- **Prompt-Response 测试**：构造几组对话历史+用户输入，通过调用模型接口，断言返回 JSON 字段格式正确，槽位及函数名符合预期。
- **端到端模拟**：使用脚本模拟整个交互，将函数调用结果注入，验证 LLM 最终回复包含正确下单确认。

---

**总结**：此方案通过纯大模型 Prompt Chain 和函数调用能力，省去自研 NLU、Context Management、Debug Agent 等模块，大大提升开发效率，但需要依赖高质量 Prompt 和强上下文窗口支持。

