#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试随机session_id和上下文管理
验证大模型的Function Calling和对话连续性
"""

import json
import uuid
import time
import random
from taxi_agent_system import EnhancedTaxiAgent


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def test_random_session_isolation():
    """测试随机session隔离"""
    print("=== 随机Session隔离测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 创建5个随机session
    sessions = []
    for i in range(5):
        session_id = generate_session_id()
        user_name = f"用户{i+1}"
        sessions.append({
            "id": session_id,
            "name": user_name,
            "messages": []
        })
    
    print("创建的随机会话:")
    for session in sessions:
        print(f"  {session['name']}: {session['id']}")
    
    # 每个session发送不同的消息
    test_messages = [
        "你好，我是新用户",
        "康德大厦在哪里？",
        "北京有哪些星巴克？",
        "帮我查一下杭州的城市代码",
        "我要从康德大厦打车到太阳宫"
    ]
    
    print(f"\n发送测试消息:")
    for i, session in enumerate(sessions):
        message = test_messages[i]
        session_id = session["id"]
        user_name = session["name"]
        
        print(f"\n{user_name} ({session_id[:12]}...): {message}")
        
        try:
            start_time = time.time()
            response = agent.process_message(message, session_id)
            end_time = time.time()
            
            print(f"回复: {response[:100]}...")
            print(f"响应时间: {end_time - start_time:.2f}s")
            
            # 记录消息
            session["messages"].append({
                "user": message,
                "assistant": response,
                "time": end_time - start_time
            })
            
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        time.sleep(1)  # 避免请求过快
    
    # 验证session隔离
    print(f"\n📊 Session隔离验证:")
    print(f"总会话数: {len(agent.context)}")
    
    for session in sessions:
        session_id = session["id"]
        if session_id in agent.context:
            context_length = len(agent.context[session_id])
            print(f"  {session['name']}: {context_length} 条消息")
        else:
            print(f"  {session['name']}: 未找到上下文")
    
    # 检查上下文独立性
    unique_contexts = set()
    for session_id, context in agent.context.items():
        context_str = str(context)
        unique_contexts.add(context_str)
    
    if len(unique_contexts) == len(agent.context):
        print("✅ 所有会话完全隔离")
    else:
        print("⚠️  检测到会话上下文重叠")
    
    return sessions, agent


def test_context_continuity():
    """测试上下文连续性"""
    print("\n=== 上下文连续性测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟连续对话
    conversation_flow = [
        "你好，我想了解打车服务",
        "康德大厦在哪里？",
        "我要从康德大厦出发",
        "目的地是太阳宫",
        "车辆选择舒适型",
        "确认订单",
        "谢谢"
    ]
    
    print(f"Session ID: {session_id}")
    print("模拟连续对话流程:")
    
    conversation_history = []
    
    for i, message in enumerate(conversation_flow, 1):
        print(f"\n--- 轮次 {i} ---")
        print(f"👤 用户: {message}")
        
        try:
            start_time = time.time()
            response = agent.process_message(message, session_id)
            end_time = time.time()
            
            print(f"🤖 助手: {response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}s")
            
            # 显示上下文长度
            if session_id in agent.context:
                context_length = len(agent.context[session_id])
                print(f"📝 上下文长度: {context_length} 条消息")
            
            conversation_history.append({
                "turn": i,
                "user": message,
                "assistant": response,
                "response_time": end_time - start_time,
                "context_length": len(agent.context.get(session_id, []))
            })
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
            
            # 使用DebugAgent分析错误
            agent.debug_agent.log_error(str(e), {
                "turn": i,
                "message": message,
                "session_id": session_id
            })
            
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")
        
        time.sleep(1)
    
    # 分析对话质量
    print(f"\n📊 对话质量分析:")
    if conversation_history:
        avg_response_time = sum(h["response_time"] for h in conversation_history) / len(conversation_history)
        max_context = max(h["context_length"] for h in conversation_history)
        successful_turns = len(conversation_history)
        
        print(f"  成功轮次: {successful_turns}/{len(conversation_flow)}")
        print(f"  平均响应时间: {avg_response_time:.2f}s")
        print(f"  最大上下文长度: {max_context} 条消息")
        print(f"  上下文增长: 正常" if max_context > 0 else "异常")
    
    return conversation_history, agent


def test_concurrent_sessions_with_context():
    """测试并发会话的上下文管理"""
    print("\n=== 并发会话上下文管理测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 创建3个用户的并发会话
    users = [
        {
            "name": "商务用户",
            "id": generate_session_id(),
            "conversations": [
                "你好，我需要商务出行服务",
                "从康德大厦到机场",
                "选择豪华型车辆",
                "预计多长时间？"
            ]
        },
        {
            "name": "旅游用户",
            "id": generate_session_id(),
            "conversations": [
                "我是游客，想了解北京景点",
                "天安门在哪里？",
                "从天安门到故宫怎么走？",
                "附近有什么好吃的？"
            ]
        },
        {
            "name": "本地用户",
            "id": generate_session_id(),
            "conversations": [
                "帮我查一下北京的城市代码",
                "我要从家里出发",
                "目的地是公司",
                "经济型车辆就可以"
            ]
        }
    ]
    
    print("并发用户:")
    for user in users:
        print(f"  {user['name']}: {user['id'][:12]}...")
    
    # 模拟交替对话
    max_turns = max(len(u["conversations"]) for u in users)
    
    for turn in range(max_turns):
        print(f"\n--- 并发轮次 {turn + 1} ---")
        
        for user in users:
            if turn < len(user["conversations"]):
                user_name = user["name"]
                session_id = user["id"]
                message = user["conversations"][turn]
                
                print(f"\n{user_name}: {message}")
                
                try:
                    start_time = time.time()
                    response = agent.process_message(message, session_id)
                    end_time = time.time()
                    
                    print(f"回复: {response[:80]}...")
                    print(f"时间: {end_time - start_time:.2f}s")
                    
                    # 记录上下文信息
                    if session_id in agent.context:
                        context_length = len(agent.context[session_id])
                        print(f"上下文: {context_length} 条")
                    
                except Exception as e:
                    print(f"❌ 失败: {e}")
                
                time.sleep(0.5)
    
    # 分析并发会话状态
    print(f"\n📊 并发会话分析:")
    print(f"总会话数: {len(agent.context)}")
    
    for user in users:
        session_id = user["id"]
        if session_id in agent.context:
            context = agent.context[session_id]
            user_messages = 0
            assistant_messages = 0

            for msg in context:
                if isinstance(msg, dict):
                    role = msg.get("role")
                else:
                    role = getattr(msg, "role", None)

                if role == "user":
                    user_messages += 1
                elif role == "assistant":
                    assistant_messages += 1

            print(f"  {user['name']}:")
            print(f"    总消息: {len(context)} 条")
            print(f"    用户消息: {user_messages} 条")
            print(f"    助手回复: {assistant_messages} 条")
    
    return users, agent


def test_session_memory_and_diagnosis():
    """测试会话记忆和诊断功能"""
    print("\n=== 会话记忆和诊断测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试记忆功能
    memory_test_messages = [
        "我的名字是张三",
        "我住在康德大厦",
        "我经常去太阳宫上班",
        "我的名字是什么？",
        "我住在哪里？",
        "我在哪里上班？"
    ]
    
    print(f"Session ID: {session_id}")
    print("测试会话记忆:")
    
    for i, message in enumerate(memory_test_messages, 1):
        print(f"\n--- 记忆测试 {i} ---")
        print(f"用户: {message}")
        
        try:
            response = agent.process_message(message, session_id)
            print(f"助手: {response}")
            
        except Exception as e:
            print(f"❌ 失败: {e}")
            agent.debug_agent.log_error(str(e), {
                "memory_test": i,
                "message": message
            })
        
        time.sleep(1)
    
    # 诊断系统状态
    print(f"\n🔍 系统诊断:")
    diagnosis = agent.debug_agent.get_diagnosis()
    print(diagnosis)
    
    # 性能报告
    print(f"\n📊 性能报告:")
    performance = agent.debug_agent.get_performance_report()
    for key, value in performance.items():
        print(f"  {key}: {value}")
    
    # 修正建议
    print(f"\n🔧 修正建议:")
    suggestions = agent.debug_agent.get_fix_suggestions()
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")
    
    return agent


def main():
    """主测试函数"""
    print("随机Session和上下文管理测试")
    print("=" * 60)
    
    # 1. 测试随机session隔离
    sessions, agent1 = test_random_session_isolation()
    
    # 2. 测试上下文连续性
    conversation_history, agent2 = test_context_continuity()
    
    # 3. 测试并发会话
    users, agent3 = test_concurrent_sessions_with_context()
    
    # 4. 测试会话记忆和诊断
    agent4 = test_session_memory_and_diagnosis()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    print("✅ 测试完成项目:")
    print("1. 随机Session ID生成和隔离")
    print("2. 上下文连续性管理")
    print("3. 并发会话处理")
    print("4. 会话记忆功能")
    print("5. 系统诊断和性能监控")
    
    print(f"\n📊 测试统计:")
    print(f"  随机会话创建: {len(sessions)} 个")
    print(f"  连续对话轮次: {len(conversation_history)} 轮")
    print(f"  并发用户数: {len(users)} 个")
    print(f"  系统诊断: 正常运行")
    
    print(f"\n🎯 验证结果:")
    print("✅ 随机session_id支持正常")
    print("✅ 上下文管理功能正常")
    print("✅ Function Calling执行正常")
    print("✅ 大模型对话集成正常")
    print("✅ 错误诊断和修正建议正常")


if __name__ == "__main__":
    main()
