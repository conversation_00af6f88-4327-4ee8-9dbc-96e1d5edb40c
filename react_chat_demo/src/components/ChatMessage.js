import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON>, MapP<PERSON>, Clock, AlertCircle } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

const MessageContainer = styled(motion.div)`
  display: flex;
  gap: 12px;
  align-items: flex-start;
  margin-bottom: 16px;
`;

const Avatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => props.isUser ? 
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 
    'linear-gradient(135deg, #10b981 0%, #059669 100%)'
  };
  color: white;
  flex-shrink: 0;
`;

const MessageContent = styled.div`
  flex: 1;
  max-width: calc(100% - 60px);
`;

const MessageBubble = styled.div`
  background: ${props => props.isUser ? 
    'rgba(102, 126, 234, 0.1)' : 
    props.isError ? 'rgba(239, 68, 68, 0.1)' : 'rgba(255, 255, 255, 0.9)'
  };
  border: 1px solid ${props => props.isUser ? 
    'rgba(102, 126, 234, 0.2)' : 
    props.isError ? 'rgba(239, 68, 68, 0.2)' : 'rgba(255, 255, 255, 0.3)'
  };
  border-radius: 16px;
  padding: 12px 16px;
  color: ${props => props.isUser ? 'white' : '#333'};
  backdrop-filter: blur(10px);
  word-wrap: break-word;
  
  /* Markdown样式 */
  p {
    margin: 0 0 8px 0;
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
  }
  
  li {
    margin: 4px 0;
  }
  
  code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.9em;
  }
  
  pre {
    background: rgba(0, 0, 0, 0.1);
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 8px 0;
  }
`;

const MessageMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 6px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
`;

const DataContainer = styled.div`
  margin-top: 12px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  border-left: 3px solid #10b981;
`;

const DataTitle = styled.div`
  font-weight: 600;
  margin-bottom: 8px;
  color: #10b981;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const DataContent = styled.pre`
  font-size: 12px;
  color: #666;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', monospace;
`;

const LocationInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 8px;
  margin-top: 8px;
  font-size: 14px;
  color: #059669;
`;

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const extractLocationData = (data) => {
  if (!data) return null;
  
  // 尝试从不同的数据结构中提取位置信息
  if (data.coordinates) {
    return {
      type: 'coordinates',
      lat: data.coordinates.lat,
      lng: data.coordinates.lng,
      address: data.address
    };
  }
  
  if (data.location) {
    return {
      type: 'location',
      ...data.location
    };
  }
  
  return null;
};

const ChatMessage = ({ message }) => {
  const isUser = message.role === 'user';
  const locationData = extractLocationData(message.data);
  
  return (
    <MessageContainer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Avatar isUser={isUser}>
        {isUser ? <User size={20} /> : <Bot size={20} />}
      </Avatar>
      
      <MessageContent>
        <MessageBubble isUser={isUser} isError={message.isError}>
          {message.isError && (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: '6px', 
              marginBottom: '8px',
              color: '#ef4444'
            }}>
              <AlertCircle size={16} />
              <span style={{ fontWeight: '600' }}>错误</span>
            </div>
          )}
          
          <ReactMarkdown>{message.content}</ReactMarkdown>
          
          {locationData && (
            <LocationInfo>
              <MapPin size={16} />
              {locationData.type === 'coordinates' ? (
                <span>
                  坐标: {locationData.lat}, {locationData.lng}
                  {locationData.address && ` (${locationData.address})`}
                </span>
              ) : (
                <span>位置信息已获取</span>
              )}
            </LocationInfo>
          )}
        </MessageBubble>
        
        <MessageMeta>
          <Clock size={12} />
          <span>{formatTimestamp(message.timestamp)}</span>
          {message.data && !locationData && (
            <>
              <span>•</span>
              <span>包含数据</span>
            </>
          )}
        </MessageMeta>
        
        {message.data && !locationData && (
          <DataContainer>
            <DataTitle>
              📊 详细信息
            </DataTitle>
            <DataContent>
              {JSON.stringify(message.data, null, 2)}
            </DataContent>
          </DataContainer>
        )}
      </MessageContent>
    </MessageContainer>
  );
};

export default ChatMessage;
