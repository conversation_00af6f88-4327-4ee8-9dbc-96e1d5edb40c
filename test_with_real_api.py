#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实API测试脚本
配置真实API密钥后的完整测试
"""

import os
import json
import uuid
import time
from taxi_agent_system import EnhancedTaxiAgent


def setup_api_keys():
    """设置API密钥（示例）"""
    print("=== API密钥配置 ===")
    print("请设置以下环境变量：")
    print()
    print("1. 高德地图API密钥:")
    print("   export AMAP_API_KEY='your_amap_api_key'")
    print("   获取地址: https://console.amap.com/")
    print()
    print("2. 百炼AI模型API密钥:")
    print("   export BAILIAN_API_KEY='your_bailian_api_key'")
    print("   获取地址: https://dashscope.aliyuncs.com/")
    print()
    
    # 检查当前配置
    amap_key = os.getenv("AMAP_API_KEY")
    bailian_key = os.getenv("BAILIAN_API_KEY")
    
    print("当前配置状态:")
    print(f"  AMAP_API_KEY: {'✅ 已配置' if amap_key else '❌ 未配置'}")
    print(f"  BAILIAN_API_KEY: {'✅ 已配置' if bailian_key else '❌ 未配置'}")
    
    return bool(amap_key), bool(bailian_key)


def test_real_conversation():
    """测试真实对话（需要API密钥）"""
    print("\n=== 真实大模型对话测试 ===")
    
    has_amap, has_bailian = setup_api_keys()
    
    if not has_bailian:
        print("⚠️  跳过真实对话测试 (需要 BAILIAN_API_KEY)")
        return
    
    agent = EnhancedTaxiAgent()
    session_id = f"real_session_{uuid.uuid4().hex[:8]}"
    
    test_conversations = [
        "你好，我是新用户",
        "康德大厦在哪里？",
        "我要从康德大厦打车到太阳宫，要舒适型车辆",
        "北京有哪些星巴克？",
        "帮我查一下北京的城市代码",
        "谢谢你的帮助"
    ]
    
    print(f"Session ID: {session_id}")
    
    for i, message in enumerate(test_conversations, 1):
        print(f"\n--- 对话轮次 {i} ---")
        print(f"👤 用户: {message}")
        
        try:
            start_time = time.time()
            response = agent.process_message(message, session_id)
            end_time = time.time()
            
            print(f"🤖 助手: {response}")
            print(f"⏱️  响应时间: {end_time - start_time:.2f}秒")
            
            # 显示上下文信息
            if session_id in agent.context:
                context_length = len(agent.context[session_id])
                print(f"📝 上下文长度: {context_length} 条消息")
            
            # 显示状态
            status = agent.state.current_status
            print(f"📊 状态: {status['status']}")
            if status.get('error'):
                print(f"❌ 错误: {status['error']}")
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
            
            # 使用 DebugAgent 分析错误
            agent.debug_agent.log_error(str(e), {
                "message": message,
                "session_id": session_id,
                "conversation_turn": i
            })
            
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")
            
            # 提供修正建议
            provide_error_fix_suggestions(agent, str(e))
        
        time.sleep(1)  # 避免请求过快


def test_concurrent_sessions():
    """测试并发会话"""
    print("\n=== 并发会话测试 ===")
    
    has_amap, has_bailian = setup_api_keys()
    
    if not has_bailian:
        print("⚠️  跳过并发会话测试 (需要 BAILIAN_API_KEY)")
        return
    
    agent = EnhancedTaxiAgent()
    
    # 模拟3个用户同时使用
    sessions = [
        {
            "user": "用户A",
            "id": f"session_a_{uuid.uuid4().hex[:8]}",
            "messages": ["康德大厦在哪里？", "我要打车去机场"]
        },
        {
            "user": "用户B",
            "id": f"session_b_{uuid.uuid4().hex[:8]}",
            "messages": ["北京有哪些星巴克？", "我要从天安门到故宫"]
        },
        {
            "user": "用户C",
            "id": f"session_c_{uuid.uuid4().hex[:8]}",
            "messages": ["查一下杭州的城市代码", "我要从西湖打车到杭州东站"]
        }
    ]
    
    # 交替处理不同用户的消息
    max_turns = max(len(s["messages"]) for s in sessions)
    
    for turn in range(max_turns):
        print(f"\n--- 轮次 {turn + 1} ---")
        
        for session_info in sessions:
            if turn < len(session_info["messages"]):
                user = session_info["user"]
                session_id = session_info["id"]
                message = session_info["messages"][turn]
                
                print(f"\n{user} ({session_id[:12]}...): {message}")
                
                try:
                    response = agent.process_message(message, session_id)
                    print(f"回复: {response}")
                    
                except Exception as e:
                    print(f"❌ 失败: {e}")
                    agent.debug_agent.log_error(str(e), {
                        "user": user,
                        "session_id": session_id,
                        "message": message
                    })
                
                time.sleep(0.5)
    
    # 显示会话统计
    print(f"\n📊 会话统计:")
    print(f"  总会话数: {len(agent.context)}")
    for session_id, context in agent.context.items():
        print(f"  {session_id[:12]}...: {len(context)} 条消息")


def analyze_system_performance(agent):
    """分析系统性能"""
    print("\n=== 系统性能分析 ===")
    
    # 执行历史分析
    history = agent.state.execution_history
    if not history:
        print("📊 暂无执行历史")
        return
    
    print(f"📊 总执行次数: {len(history)}")
    
    # 统计成功率
    success_count = sum(1 for h in history if h['status'] == 'success')
    failed_count = sum(1 for h in history if h['status'] == 'failed')
    success_rate = (success_count / len(history)) * 100 if history else 0
    
    print(f"✅ 成功: {success_count} 次 ({success_rate:.1f}%)")
    print(f"❌ 失败: {failed_count} 次")
    
    # 分析执行时间（如果有的话）
    processing_times = []
    for record in history:
        if 'processing_time' in record.get('details', {}):
            processing_times.append(record['details']['processing_time'])
    
    if processing_times:
        avg_time = sum(processing_times) / len(processing_times)
        print(f"⏱️  平均处理时间: {avg_time:.2f}秒")
    
    # 最近的执行状态
    print("\n最近执行:")
    for record in history[-5:]:
        status_icon = "✅" if record['status'] == 'success' else "❌"
        timestamp = record['timestamp'][:19]  # 去掉毫秒
        print(f"  {status_icon} {timestamp} - {record['action']}")


def provide_error_fix_suggestions(agent, error_message):
    """提供错误修正建议"""
    print("\n🔧 修正建议:")
    
    error_lower = error_message.lower()
    
    if 'api_key' in error_lower or 'authentication' in error_lower:
        print("1. API密钥问题:")
        print("   - 检查环境变量是否正确设置")
        print("   - 验证API密钥是否有效")
        print("   - 确认API服务是否正常")
        print("   - 检查API配额是否用完")
    
    elif 'network' in error_lower or 'connection' in error_lower or 'timeout' in error_lower:
        print("2. 网络连接问题:")
        print("   - 检查网络连接状态")
        print("   - 尝试重新连接")
        print("   - 检查防火墙设置")
        print("   - 考虑增加超时时间")
    
    elif 'rate limit' in error_lower or 'quota' in error_lower:
        print("3. API限制问题:")
        print("   - 降低请求频率")
        print("   - 检查API配额")
        print("   - 考虑升级API套餐")
        print("   - 实现请求重试机制")
    
    elif 'function' in error_lower or 'parameter' in error_lower:
        print("4. 函数调用问题:")
        print("   - 检查函数参数格式")
        print("   - 验证必需参数是否提供")
        print("   - 确认参数类型正确")
        print("   - 查看函数定义文档")
    
    else:
        print("5. 通用建议:")
        print("   - 查看详细错误日志")
        print("   - 检查系统资源使用情况")
        print("   - 尝试重启服务")
        print("   - 联系技术支持")
    
    # 检查错误日志中的模式
    errors = agent.debug_agent.error_log
    if len(errors) > 1:
        recent_errors = [e['error'] for e in errors[-5:]]
        if len(set(recent_errors)) == 1:
            print("\n⚠️  检测到重复错误，建议:")
            print("   - 这可能是系统性问题")
            print("   - 检查根本原因而非症状")
            print("   - 考虑暂停服务进行修复")


def comprehensive_test():
    """综合测试"""
    print("=== 综合测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 1. 基础功能测试
    print("\n1. 基础功能测试")
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {"start_place": "康德大厦", "end_place": "太阳宫", "car_prefer": ""},
            "test_session"
        )
        print("✅ 打车服务正常")
    except Exception as e:
        print(f"❌ 打车服务异常: {e}")
    
    # 2. 状态管理测试
    print("\n2. 状态管理测试")
    initial_history_count = len(agent.state.execution_history)
    agent.state.log_execution("test_action", "success", {"test": True})
    final_history_count = len(agent.state.execution_history)
    
    if final_history_count > initial_history_count:
        print("✅ 状态管理正常")
    else:
        print("❌ 状态管理异常")
    
    # 3. 错误处理测试
    print("\n3. 错误处理测试")
    try:
        agent._execute_function("invalid_function", {}, "test_session")
    except Exception as e:
        print("✅ 错误处理正常")
    
    print("\n综合测试完成")


def main():
    """主测试函数"""
    print("真实API集成测试")
    print("=" * 60)
    
    # 1. 设置和检查API密钥
    has_amap, has_bailian = setup_api_keys()
    
    # 2. 综合测试
    comprehensive_test()
    
    # 3. 真实对话测试
    if has_bailian:
        test_real_conversation()
    
    # 4. 并发会话测试
    if has_bailian:
        test_concurrent_sessions()
    
    # 5. 性能分析
    agent = EnhancedTaxiAgent()
    # 运行一些测试来生成数据
    try:
        agent.process_message("测试消息", "perf_test")
    except:
        pass
    
    analyze_system_performance(agent)
    
    print("\n" + "=" * 60)
    print("真实API测试完成")
    
    if not has_bailian:
        print("\n💡 提示: 配置 BAILIAN_API_KEY 后可进行完整的AI对话测试")
    if not has_amap:
        print("💡 提示: 配置 AMAP_API_KEY 后可进行完整的地图功能测试")


if __name__ == "__main__":
    main()
