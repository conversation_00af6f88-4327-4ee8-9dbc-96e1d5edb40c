#!/usr/bin/env python3
"""
演示系统测试脚本
用于验证各个组件的功能是否正常
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

def test_imports():
    """测试核心模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        print("✅ taxi_agent_system 导入成功")
    except ImportError as e:
        print(f"❌ taxi_agent_system 导入失败: {e}")
        return False
    
    try:
        from amap_mcp_tools import AmapMCPTools
        print("✅ amap_mcp_tools 导入成功")
    except ImportError as e:
        print(f"❌ amap_mcp_tools 导入失败: {e}")
        return False
    
    try:
        import streamlit
        print("✅ streamlit 导入成功")
    except ImportError as e:
        print(f"❌ streamlit 导入失败: {e}")
        return False
    
    try:
        import flask
        print("✅ flask 导入成功")
    except ImportError as e:
        print(f"❌ flask 导入失败: {e}")
        return False
    
    return True

def test_environment():
    """测试环境变量"""
    print("\n🔍 测试环境变量...")
    
    required_vars = ['BAILIAN_API_KEY', 'AMAP_API_KEY']
    all_set = True
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * (len(value) - 4) + value[-4:]}")
        else:
            print(f"❌ {var}: 未设置")
            all_set = False
    
    return all_set

def test_taxi_agent():
    """测试打车Agent基本功能"""
    print("\n🔍 测试打车Agent...")
    
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        
        agent = EnhancedTaxiAgent()
        print("✅ Agent初始化成功")
        
        # 测试简单查询
        test_message = "你好"
        print(f"📤 发送测试消息: {test_message}")
        
        response = agent.process_message(test_message, "test_session")
        print(f"📥 收到响应: {response[:100]}...")
        
        if response and len(response) > 0:
            print("✅ Agent响应正常")
            return True
        else:
            print("❌ Agent响应异常")
            return False
            
    except Exception as e:
        print(f"❌ Agent测试失败: {e}")
        return False

def test_api_server():
    """测试API服务器"""
    print("\n🔍 测试API服务器...")
    
    base_url = "http://localhost:8000"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            data = response.json()
            print(f"   状态: {data.get('status')}")
            print(f"   时间: {data.get('timestamp')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器 (可能未启动)")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False
    
    # 测试聊天接口
    try:
        chat_data = {
            "message": "测试消息",
            "session_id": "test_session"
        }
        
        response = requests.post(
            f"{base_url}/api/chat",
            json=chat_data,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 聊天接口正常")
            data = response.json()
            print(f"   响应: {data.get('response', '')[:50]}...")
        else:
            print(f"❌ 聊天接口失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 聊天接口测试失败: {e}")
        return False
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 测试文件结构...")
    
    required_files = [
        'taxi_agent_system.py',
        'amap_mcp_tools.py',
        'taxi_demo_app.py',
        'api_server.py',
        'start_demo.py',
        'requirements.txt',
        'README_DEMO.md'
    ]
    
    required_dirs = [
        'react_chat_demo',
        'react_chat_demo/src',
        'react_chat_demo/src/components'
    ]
    
    all_present = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} 缺失")
            all_present = False
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ 缺失")
            all_present = False
    
    return all_present

def run_integration_test():
    """运行集成测试"""
    print("\n🔍 运行集成测试...")
    
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        
        agent = EnhancedTaxiAgent()
        session_id = f"integration_test_{int(time.time())}"
        
        test_cases = [
            "你好",
            "西湖在哪里？",
            "帮我查一下杭州的城市代码"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"   测试 {i}: {test_case}")
            
            start_time = time.time()
            response = agent.process_message(test_case, session_id)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            if response and len(response) > 0:
                print(f"   ✅ 响应正常 ({response_time:.0f}ms)")
                print(f"      {response[:80]}...")
            else:
                print(f"   ❌ 响应异常")
                return False
        
        print("✅ 集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚗 智能打车助手演示系统测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("环境变量", test_environment),
        ("文件结构", test_file_structure),
        ("打车Agent", test_taxi_agent),
        ("集成测试", run_integration_test),
        ("API服务器", test_api_server)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results[test_name] = False
    
    # 输出测试总结
    print("\n" + "="*50)
    print("📊 测试总结")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
