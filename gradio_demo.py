"""
Gradio版本的智能打车助手演示
提供简单易用的Web界面
"""

import gradio as gr
import json
import time
import uuid
from datetime import datetime
from typing import List, Tuple

# 导入我们的打车系统
try:
    from taxi_agent_system import EnhancedTaxiAgent
    TAXI_AGENT_AVAILABLE = True
except ImportError as e:
    print(f"警告: 无法导入打车系统: {e}")
    TAXI_AGENT_AVAILABLE = False

# 全局变量
taxi_agent = None
conversation_history = []

def init_agent():
    """初始化打车Agent"""
    global taxi_agent
    if TAXI_AGENT_AVAILABLE and taxi_agent is None:
        try:
            taxi_agent = EnhancedTaxiAgent()
            return True
        except Exception as e:
            print(f"Agent初始化失败: {e}")
            return False
    return taxi_agent is not None

def chat_with_agent(message: str, history: List[Tuple[str, str]]) -> Tuple[str, List[Tuple[str, str]]]:
    """与Agent聊天的主函数"""
    global conversation_history
    
    if not message.strip():
        return "", history
    
    # 初始化Agent
    if not init_agent():
        error_msg = "抱歉，打车系统当前不可用。请检查配置。"
        history.append((message, error_msg))
        return "", history
    
    try:
        # 生成会话ID
        session_id = "gradio_session"
        
        # 处理消息
        start_time = time.time()
        response = taxi_agent.process_message(message, session_id)
        end_time = time.time()
        
        # 计算响应时间
        response_time = (end_time - start_time) * 1000
        
        # 添加响应时间信息
        if response_time > 1000:
            time_info = f" (耗时: {response_time/1000:.1f}秒)"
        else:
            time_info = f" (耗时: {response_time:.0f}毫秒)"
        
        response_with_time = response + time_info
        
        # 更新历史记录
        history.append((message, response_with_time))
        conversation_history = history
        
        return "", history
        
    except Exception as e:
        error_msg = f"处理请求时出错: {str(e)}"
        history.append((message, error_msg))
        return "", history

def clear_history():
    """清除对话历史"""
    global conversation_history
    conversation_history = []
    return []

def get_example_queries():
    """获取示例查询"""
    return [
        "西湖在哪里？",
        "帮我查一下杭州的城市代码",
        "北京有哪些星巴克？",
        "我要从康德大厦打车到太阳宫",
        "从北京站到首都机场，要舒适型车辆"
    ]

def handle_example_click(example: str):
    """处理示例点击"""
    return example

def get_system_status():
    """获取系统状态"""
    status_info = {
        "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "打车系统": "✅ 可用" if TAXI_AGENT_AVAILABLE and init_agent() else "❌ 不可用",
        "会话数量": len(conversation_history)
    }
    
    # 尝试获取更多状态信息
    if taxi_agent and hasattr(taxi_agent, 'debug_agent'):
        try:
            debug_info = taxi_agent.debug_agent.performance_metrics
            status_info.update({
                "总请求数": debug_info.get('total_requests', 0),
                "成功请求": debug_info.get('successful_requests', 0),
                "失败请求": debug_info.get('failed_requests', 0)
            })
        except:
            pass
    
    return json.dumps(status_info, ensure_ascii=False, indent=2)

def create_interface():
    """创建Gradio界面"""
    
    # 自定义CSS
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
        margin: auto !important;
    }
    
    .header {
        text-align: center;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .example-btn {
        margin: 5px;
        background: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 8px 12px;
        cursor: pointer;
    }
    
    .example-btn:hover {
        background: #e0e0e0;
    }
    
    .status-box {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        font-family: monospace;
        font-size: 12px;
    }
    """
    
    with gr.Blocks(css=custom_css, title="智能打车助手") as demo:
        
        # 标题和说明
        gr.HTML("""
        <div class="header">
            <h1>🚗 智能打车助手</h1>
            <p>基于高德地图API和AI技术的智能打车服务演示</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=3):
                # 主聊天界面
                chatbot = gr.Chatbot(
                    label="对话界面",
                    height=500,
                    show_label=True,
                    container=True
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        placeholder="请输入您的问题...",
                        label="消息输入",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("发送", variant="primary", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清除历史", variant="secondary")
                    
            with gr.Column(scale=1):
                # 侧边栏
                gr.Markdown("### 💡 示例查询")
                
                examples = get_example_queries()
                example_buttons = []
                
                for example in examples:
                    btn = gr.Button(example, variant="secondary", size="sm")
                    example_buttons.append(btn)
                
                gr.Markdown("### 📊 系统状态")
                status_display = gr.JSON(
                    label="状态信息",
                    value=lambda: json.loads(get_system_status())
                )
                
                refresh_btn = gr.Button("刷新状态", variant="secondary", size="sm")
                
                gr.Markdown("### 🎯 支持功能")
                gr.Markdown("""
                - 🗺️ 地点查询和坐标转换
                - 🏙️ 城市信息和代码查询
                - 📍 POI搜索和商户查找
                - 🚗 智能打车和价格估算
                """)
        
        # 事件绑定
        def submit_message(message, history):
            return chat_with_agent(message, history)
        
        # 发送消息
        send_btn.click(
            submit_message,
            inputs=[msg_input, chatbot],
            outputs=[msg_input, chatbot]
        )
        
        msg_input.submit(
            submit_message,
            inputs=[msg_input, chatbot],
            outputs=[msg_input, chatbot]
        )
        
        # 清除历史
        clear_btn.click(
            clear_history,
            outputs=[chatbot]
        )
        
        # 示例按钮点击
        for btn, example in zip(example_buttons, examples):
            btn.click(
                lambda ex=example: ex,
                outputs=[msg_input]
            )
        
        # 刷新状态
        refresh_btn.click(
            lambda: json.loads(get_system_status()),
            outputs=[status_display]
        )
        
        # 定期刷新状态（每30秒）
        demo.load(
            lambda: json.loads(get_system_status()),
            outputs=[status_display],
            every=30
        )
    
    return demo

def main():
    """主函数"""
    print("🚗 启动Gradio版智能打车助手...")
    
    # 检查系统状态
    if not TAXI_AGENT_AVAILABLE:
        print("⚠️  打车系统不可用，某些功能可能受限")
    
    if init_agent():
        print("✅ 打车Agent初始化成功")
    else:
        print("⚠️  打车Agent初始化失败")
    
    # 创建并启动界面
    demo = create_interface()
    
    print("🌐 启动Web界面...")
    print("📱 访问地址: http://localhost:7860")
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    main()
