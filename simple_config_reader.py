"""
简单的配置文件读取器
专门用于读取config.yaml中的main_process_system_prompt
"""

import re
import os


def read_main_process_system_prompt(config_path: str = "config.yaml") -> str:
    """
    读取config.yaml中的main_process_system_prompt
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        系统提示词字符串
    """
    try:
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return ""
        
        with open(config_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        print(f"📁 读取文件: {config_path}")
        print(f"📏 文件大小: {len(content)} 字符")
        
        # 查找main_process_system_prompt的值
        # 格式: "main_process_system_prompt" : '内容'
        
        # 使用正则表达式提取单引号内的内容
        pattern = r'"main_process_system_prompt"\s*:\s*\'(.*?)\'\s*$'
        match = re.search(pattern, content, re.DOTALL | re.MULTILINE)
        
        if match:
            prompt_content = match.group(1)
            # 处理转义字符
            prompt_content = prompt_content.replace('\\n', '\n')
            
            print(f"✅ 成功提取系统提示词")
            print(f"📏 提示词长度: {len(prompt_content)} 字符")
            
            return prompt_content
        else:
            print("❌ 未找到main_process_system_prompt")
            return ""
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return ""


def save_system_prompt_to_file(prompt: str, output_path: str = "system_prompt.txt"):
    """
    将系统提示词保存到文件
    
    Args:
        prompt: 系统提示词内容
        output_path: 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(prompt)
        print(f"✅ 系统提示词已保存到: {output_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {str(e)}")


def create_formatted_yaml(prompt: str, output_path: str = "config_formatted.yaml"):
    """
    创建格式化的YAML配置文件
    
    Args:
        prompt: 系统提示词内容
        output_path: 输出文件路径
    """
    try:
        # 创建标准YAML格式
        yaml_content = f"""# 打车系统配置文件
main_process_system_prompt: |
  {prompt.replace(chr(10), chr(10) + '  ')}

# 其他配置项可以在这里添加
# api_settings:
#   timeout: 30
#   retries: 3
"""
        
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(yaml_content)
        print(f"✅ 格式化YAML已保存到: {output_path}")
    except Exception as e:
        print(f"❌ 保存格式化YAML失败: {str(e)}")


def main():
    """主函数"""
    print("🔧 简单配置文件读取器")
    print("=" * 50)
    
    # 读取系统提示词
    prompt = read_main_process_system_prompt()
    
    if prompt:
        print(f"\n=== 系统提示词内容预览 ===")
        # 显示前200个字符
        preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
        print(preview)
        
        print(f"\n=== 完整内容 ===")
        print(prompt)
        
        # 保存到文件
        save_system_prompt_to_file(prompt)
        
        # 创建格式化的YAML
        create_formatted_yaml(prompt)
        
        print(f"\n=== 统计信息 ===")
        print(f"总字符数: {len(prompt)}")
        print(f"行数: {prompt.count(chr(10)) + 1}")
        print(f"段落数: {prompt.count(chr(10) + chr(10)) + 1}")
        
    else:
        print("❌ 无法读取系统提示词")


if __name__ == "__main__":
    main()
