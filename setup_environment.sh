#!/bin/bash
# 用户行为模拟器环境配置脚本

echo "🚗 用户行为模拟器环境配置"
echo "================================"

# 检查是否已设置环境变量
if [ -z "$BAILIAN_API_KEY" ]; then
    echo "⚠️  BAILIAN_API_KEY 未设置"
    echo "请设置百炼API密钥："
    echo "export BAILIAN_API_KEY='your_bailian_api_key'"
    echo ""
else
    echo "✅ BAILIAN_API_KEY 已设置"
fi

if [ -z "$AMAP_API_KEY" ]; then
    echo "⚠️  AMAP_API_KEY 未设置"
    echo "请设置高德地图API密钥："
    echo "export AMAP_API_KEY='your_amap_api_key'"
    echo ""
else
    echo "✅ AMAP_API_KEY 已设置"
fi

# 检查Python依赖
echo "检查Python依赖..."
python3 -c "import openai; print('✅ openai 模块可用')" 2>/dev/null || echo "❌ 需要安装: pip install openai"

# 检查必需文件
echo ""
echo "检查必需文件..."
for file in "user_behavior_simulator.py" "taxi_agent_system.py" "demo_user_simulator.py"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 缺失"
    fi
done

echo ""
echo "配置完成后，可以运行以下命令："
echo "1. 测试系统: python test_user_simulator.py"
echo "2. 快速演示: python test_user_simulator.py --demo"
echo "3. 完整演示: python demo_user_simulator.py"
