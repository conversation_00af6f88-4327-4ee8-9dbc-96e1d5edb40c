# 用户行为模拟智能体

## 概述

这是一个基于大语言模型的用户行为模拟智能体，专门用于测试taxi_agent_system的智能水平。该系统能够模拟5种不同类型的用户行为，生成多轮对话来全面测试系统的响应能力。

## 功能特点

### 🎭 5种用户场景模拟

1. **首次使用用户** - 有明确打车目标但信息提供不完整，不熟悉系统操作
2. **有历史记录用户** - 熟悉系统，有明确出行需求，期望快速完成任务
3. **探索型用户** - 没有明确目的地，纯探索系统功能和周边信息
4. **随意聊天用户** - 话题跳跃，不专注于打车需求，测试系统边界
5. **超出能力范围用户** - 提出系统无法处理的需求，测试系统限制

### 🤖 智能对话生成

- 使用百炼大模型生成符合用户特征的自然语言输入
- 支持多轮对话上下文理解
- 考虑语音输入特点（口语化、停顿词等）
- 自动回退机制，无大模型时使用预设问题

### 📊 测试分析功能

- 自动生成测试场景和对话
- 分析对话成功率和轮次统计
- 按用户类型分类统计
- 生成详细的测试报告

## 安装和配置

### 环境要求

```bash
pip install openai
```

### 环境变量配置

```bash
# 百炼API密钥（必需）
export BAILIAN_API_KEY="your_bailian_api_key"

# 高德地图API密钥（taxi_agent_system需要）
export AMAP_API_KEY="your_amap_api_key"
```

## 使用方法

### 1. 快速开始

```bash
python demo_user_simulator.py
```

运行后会显示交互菜单，选择不同的演示模式。

### 2. 编程接口使用

```python
from user_behavior_simulator import UserBehaviorSimulator

# 初始化模拟器
simulator = UserBehaviorSimulator()

# 创建会话并分配场景
session_id = simulator.generate_session_id()
simulator.assign_scenario_to_session(session_id, 1)  # 1=首次使用用户

# 生成用户输入
user_input = simulator.generate_user_input(session_id, "", 1)
print(f"用户输入: {user_input}")

# 直接调用MainAgent（参考taxi_agent_system.py）
response = simulator.taxi_agent.process_user_input(user_input)
print(f"系统回复: {response}")

# 模拟完整对话
conversation = simulator.simulate_conversation(session_id, max_turns=5)
```

### 3. 批量测试

```python
# 生成测试场景（每种用户类型2个会话）
test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=2)

# 分析结果
analysis = simulator.analyze_test_results(test_scenarios)
simulator.print_analysis_report(analysis)

# 保存结果
filename = simulator.save_test_results(test_scenarios)
```

## 核心类和方法

### UserBehaviorSimulator

主要的用户行为模拟器类，自动集成`taxi_agent_system.py`中的`MainAgent`。

#### 系统集成

模拟器在初始化时会自动创建`MainAgent`实例：

```python
from taxi_agent_system import MainAgent

class UserBehaviorSimulator:
    def __init__(self):
        # 自动集成MainAgent
        self.taxi_agent = MainAgent()
```

所有的系统回复都通过`MainAgent.process_user_input()`方法获取，完全兼容原有的taxi_agent_system接口。

#### 主要方法

- `generate_session_id()` - 生成唯一会话ID
- `assign_scenario_to_session(session_id, scenario_type)` - 为会话分配用户场景
- `generate_user_input(session_id, context, turn_number)` - 生成用户输入
- `simulate_conversation(session_id, max_turns)` - 模拟完整对话
- `generate_test_scenarios(num_sessions_per_type)` - 批量生成测试场景
- `analyze_test_results(test_scenarios)` - 分析测试结果

## 输出格式

### 会话记录格式

```json
{
  "session_id": "uuid",
  "scenario_type": 1,
  "scenario_name": "首次使用用户",
  "conversation": [
    {
      "turn": 1,
      "user_input": "你好，我想打车",
      "system_response": "您好！我可以帮您叫车...",
      "timestamp": "2024-01-01T12:00:00"
    }
  ],
  "summary": {
    "total_turns": 3,
    "is_successful": true,
    "conversation_length": 150
  }
}
```

### 分析报告格式

```
整体统计:
  总场景数: 10
  成功场景数: 8
  成功率: 80.0%
  平均轮次: 3.2

按场景类型统计:
  1. 首次使用用户:
    成功率: 75.0%
    平均轮次: 3.5
```

## 使用场景

### 1. 系统测试

- 测试打车系统对不同用户类型的适应性
- 验证系统的鲁棒性和用户体验
- 发现潜在的交互问题

### 2. 功能验证

- 验证function calling的准确性
- 测试参数补全和错误处理
- 评估系统的智能水平

### 3. 性能评估

- 统计对话成功率
- 分析平均交互轮次
- 比较不同场景下的表现

## 扩展和定制

### 添加新的用户场景

```python
# 在user_scenarios字典中添加新场景
self.user_scenarios[6] = {
    "name": "新用户类型",
    "description": "场景描述",
    "characteristics": ["特征1", "特征2"]
}
```

### 自定义提示词

修改`_build_user_generation_prompt`方法来定制用户输入生成的提示词。

### 集成其他系统

替换`self.taxi_agent`为其他需要测试的对话系统。

## 注意事项

1. **API配置**: 确保正确设置BAILIAN_API_KEY环境变量
2. **网络连接**: 大模型调用需要稳定的网络连接
3. **成本控制**: 批量测试会产生API调用费用，建议先小规模测试
4. **结果保存**: 测试结果会自动保存为JSON文件，注意存储空间

## 故障排除

### 常见问题

1. **"未设置BAILIAN_API_KEY"警告**
   - 设置环境变量或使用预设问题模式

2. **"生成用户输入失败"错误**
   - 检查网络连接和API密钥
   - 系统会自动回退到预设问题

3. **系统回复出错**
   - 检查taxi_agent_system的配置
   - 确保AMAP_API_KEY正确设置

## 更新日志

- v1.0: 初始版本，支持5种用户场景模拟
- 支持批量测试和结果分析
- 提供交互式演示界面
