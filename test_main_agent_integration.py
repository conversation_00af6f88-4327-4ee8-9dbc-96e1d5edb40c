#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MainAgent集成
验证用户行为模拟器与MainAgent的集成是否正确
"""

import os
from taxi_agent_system import MainAgent
from user_behavior_simulator import UserBehaviorSimulator


def test_main_agent_direct():
    """直接测试MainAgent"""
    print("=== 直接测试MainAgent ===")
    
    try:
        agent = MainAgent()
        response = agent.process_user_input("我要从康德大厦打车到太阳宫")
        print(f"✅ MainAgent直接调用成功")
        print(f"输入: 我要从康德大厦打车到太阳宫")
        print(f"输出: {response}")
        return True
    except Exception as e:
        print(f"❌ MainAgent直接调用失败: {e}")
        return False


def test_simulator_with_main_agent():
    """测试模拟器与MainAgent的集成"""
    print("\n=== 测试模拟器与MainAgent集成 ===")
    
    try:
        # 初始化模拟器
        simulator = UserBehaviorSimulator()
        print("✅ 模拟器初始化成功")
        
        # 检查taxi_agent是否是MainAgent实例
        if isinstance(simulator.taxi_agent, MainAgent):
            print("✅ taxi_agent是MainAgent实例")
        else:
            print(f"❌ taxi_agent类型错误: {type(simulator.taxi_agent)}")
            return False
        
        # 创建会话并生成用户输入
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, 2)  # 有历史记录用户
        
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"✅ 用户输入生成: {user_input}")
        
        # 通过模拟器调用MainAgent
        system_response = simulator.taxi_agent.process_user_input(user_input)
        print(f"✅ 系统回复: {system_response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_conversation_flow():
    """测试完整对话流程"""
    print("\n=== 测试完整对话流程 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        # 测试不同场景
        test_cases = [
            (1, "首次使用用户"),
            (2, "有历史记录用户"),
            (3, "探索型用户")
        ]
        
        for scenario_type, scenario_name in test_cases:
            print(f"\n测试场景: {scenario_name}")
            
            session_id = simulator.generate_session_id()
            simulator.assign_scenario_to_session(session_id, scenario_type)
            
            # 模拟2轮对话
            context = ""
            for turn in range(1, 3):
                user_input = simulator.generate_user_input(session_id, context, turn)
                system_response = simulator.taxi_agent.process_user_input(user_input)
                
                print(f"  第{turn}轮 - 用户: {user_input}")
                print(f"         系统: {system_response[:50]}...")
                
                context = f"用户说：{user_input}\n系统回复：{system_response}"
        
        print("✅ 对话流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        return False


def test_batch_simulation():
    """测试批量模拟"""
    print("\n=== 测试批量模拟 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        # 生成小规模测试场景
        test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=1)
        
        print(f"✅ 批量生成成功，共{len(test_scenarios)}个场景")
        
        # 检查每个场景的结果
        for scenario in test_scenarios:
            scenario_name = scenario['scenario_name']
            conversation = scenario['conversation']
            
            print(f"  {scenario_name}: {len(conversation)}轮对话")
            if conversation:
                first_turn = conversation[0]
                print(f"    首轮 - 用户: {first_turn['user_input']}")
                print(f"           系统: {first_turn['system_response'][:30]}...")
        
        # 分析结果
        analysis = simulator.analyze_test_results(test_scenarios)
        print(f"✅ 结果分析完成，成功率: {analysis['overall_stats']['success_rate']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量模拟测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚗 MainAgent集成测试")
    print("=" * 50)
    
    # 检查环境
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  注意: 未设置BAILIAN_API_KEY，将使用回退模式")
    
    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  注意: 未设置AMAP_API_KEY，地图功能可能受限")
    
    print()
    
    # 运行测试
    tests = [
        ("MainAgent直接调用", test_main_agent_direct),
        ("模拟器集成", test_simulator_with_main_agent),
        ("对话流程", test_conversation_flow),
        ("批量模拟", test_batch_simulation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 汇总结果
    print("=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！MainAgent集成成功。")
        print("\n现在可以使用以下命令进行完整测试:")
        print("python demo_user_simulator.py")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
