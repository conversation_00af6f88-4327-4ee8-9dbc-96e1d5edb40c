"""
高德地图MCP工具集成测试
演示如何使用地点转经纬度和城市ID功能
"""

import os
import json
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi
from amap_config import amap_config


def test_basic_functions():
    """测试基础功能"""
    print("=== 测试基础功能 ===\n")
    
    # 检查配置
    validation = amap_config.validate_config()
    if not validation["valid"]:
        print("❌ 配置验证失败：")
        for issue in validation["issues"]:
            print(f"  - {issue}")
        return False
    
    print("✅ 配置验证通过\n")
    
    try:
        # 初始化工具
        amap = AmapMCPTools()
        
        # 测试1: 地点转经纬度
        print("1. 测试地点转经纬度")
        test_places = [
            ("西湖", "杭州"),
            ("天安门", "北京"),
            ("外滩", "上海"),
            ("春熙路", "成都")
        ]
        
        for place, city in test_places:
            result = amap.geocode_address(place, city)
            if result["status"]:
                data = result["data"]
                print(f"   {place}({city}): {data['longitude']:.6f}, {data['latitude']:.6f}")
                print(f"   完整地址: {data['formatted_address']}")
            else:
                print(f"   ❌ {place}({city}): {result['error']}")
        
        print()
        
        # 测试2: 获取城市ID
        print("2. 测试获取城市ID")
        test_cities = ["杭州", "北京", "上海", "成都", "深圳"]
        
        for city in test_cities:
            result = amap.get_city_code(city)
            if result["status"]:
                data = result["data"]
                print(f"   {city}: adcode={data['adcode']}, citycode={data['city_code']}")
                if data.get('center'):
                    center = data['center']
                    print(f"   中心坐标: {center['longitude']:.6f}, {center['latitude']:.6f}")
            else:
                print(f"   ❌ {city}: {result['error']}")
        
        print()
        
        # 测试3: POI搜索
        print("3. 测试POI搜索")
        poi_tests = [
            ("星巴克", "杭州"),
            ("加油站", "北京"),
            ("医院", "上海")
        ]
        
        for keyword, city in poi_tests:
            result = amap.search_poi(keyword, city)
            if result["status"]:
                pois = result["data"]["pois"][:3]  # 只显示前3个
                print(f"   {keyword}({city}):")
                for i, poi in enumerate(pois, 1):
                    print(f"     {i}. {poi['name']} - {poi['address']}")
                    if poi['longitude'] and poi['latitude']:
                        print(f"        坐标: {poi['longitude']:.6f}, {poi['latitude']:.6f}")
            else:
                print(f"   ❌ {keyword}({city}): {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


def test_mcp_functions():
    """测试MCP函数接口"""
    print("\n=== 测试MCP函数接口 ===\n")
    
    # 测试MCP地理编码
    print("1. 测试MCP地理编码函数")
    result = mcp_geocode_address("西湖", "杭州")
    print(f"   结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 测试MCP城市代码
    print("\n2. 测试MCP城市代码函数")
    result = mcp_get_city_code("杭州")
    print(f"   结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    # 测试MCP POI搜索
    print("\n3. 测试MCP POI搜索函数")
    result = mcp_search_poi("星巴克", "杭州")
    if result["status"] and result["data"]["pois"]:
        # 只显示第一个结果的详细信息
        first_poi = result["data"]["pois"][0]
        print(f"   第一个结果: {json.dumps(first_poi, ensure_ascii=False, indent=2)}")
    else:
        print(f"   结果: {json.dumps(result, ensure_ascii=False, indent=2)}")


def test_integration_scenario():
    """测试集成场景"""
    print("\n=== 测试集成场景 ===\n")
    
    print("场景：用户想从西湖打车到杭州东站")
    
    # 步骤1：获取出发地坐标
    print("1. 获取西湖坐标...")
    origin_result = mcp_geocode_address("西湖", "杭州")
    if not origin_result["status"]:
        print(f"❌ 获取西湖坐标失败: {origin_result['error']}")
        return
    
    origin = origin_result["data"]
    print(f"   西湖坐标: {origin['longitude']:.6f}, {origin['latitude']:.6f}")
    
    # 步骤2：获取目的地坐标
    print("2. 获取杭州东站坐标...")
    dest_result = mcp_geocode_address("杭州东站", "杭州")
    if not dest_result["status"]:
        print(f"❌ 获取杭州东站坐标失败: {dest_result['error']}")
        return
    
    dest = dest_result["data"]
    print(f"   杭州东站坐标: {dest['longitude']:.6f}, {dest['latitude']:.6f}")
    
    # 步骤3：计算距离（简单计算）
    import math
    lat_diff = dest['latitude'] - origin['latitude']
    lon_diff = dest['longitude'] - origin['longitude']
    distance_km = math.sqrt(lat_diff**2 + lon_diff**2) * 111
    
    print(f"3. 计算距离: 约 {distance_km:.2f} 公里")
    
    # 步骤4：模拟价格估算
    base_price = 10
    price_per_km = 2.5
    estimated_price = base_price + distance_km * price_per_km
    
    print(f"4. 估算价格: 约 ¥{estimated_price:.2f}")
    
    print("✅ 集成场景测试完成")


def interactive_test():
    """交互式测试"""
    print("\n=== 交互式测试 ===")
    print("输入地点名称，获取经纬度坐标")
    print("输入格式：地点名称 [城市名称]")
    print("例如：西湖 杭州 或者 天安门")
    print("输入 'quit' 退出\n")
    
    while True:
        user_input = input("请输入地点: ").strip()
        if user_input.lower() == 'quit':
            break
        
        if not user_input:
            continue
        
        parts = user_input.split()
        place = parts[0]
        city = parts[1] if len(parts) > 1 else None
        
        result = mcp_geocode_address(place, city)
        if result["status"]:
            data = result["data"]
            print(f"✅ {place}: {data['longitude']:.6f}, {data['latitude']:.6f}")
            print(f"   地址: {data['formatted_address']}")
            print(f"   行政区: {data['province']} {data['city']} {data['district']}")
        else:
            print(f"❌ 错误: {result['error']}")
        print()


def main():
    """主函数"""
    print("高德地图MCP工具集成测试")
    print("=" * 50)
    
    # 基础功能测试
    if not test_basic_functions():
        print("基础功能测试失败，请检查配置")
        return
    
    # MCP函数测试
    test_mcp_functions()
    
    # 集成场景测试
    test_integration_scenario()
    
    # 交互式测试
    choice = input("\n是否进行交互式测试？(y/n): ").strip().lower()
    if choice == 'y':
        interactive_test()
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
