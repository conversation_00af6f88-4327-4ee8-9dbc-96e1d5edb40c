"""
使用示例：如何在你的代码中使用YAML配置工具
"""

from yaml_config_utils import get_main_process_system_prompt, ConfigManager, quick_get_prompt


def example_1_simple_usage():
    """示例1: 简单使用"""
    print("=== 示例1: 简单使用 ===")
    
    # 最简单的方式
    prompt = get_main_process_system_prompt()
    print(f"✅ 获取到系统提示词，长度: {len(prompt)} 字符")
    
    # 或者使用快捷函数
    prompt2 = quick_get_prompt()
    print(f"✅ 快捷函数获取，长度: {len(prompt2)} 字符")
    
    return prompt


def example_2_config_manager():
    """示例2: 使用配置管理器"""
    print("\n=== 示例2: 配置管理器 ===")
    
    # 创建配置管理器
    config = ConfigManager()
    
    # 获取系统提示词
    prompt = config.get_system_prompt()
    print(f"✅ 配置管理器获取，长度: {len(prompt)} 字符")
    
    # 获取其他配置项（如果有的话）
    timeout = config.get('timeout', 30)  # 默认值30
    print(f"✅ 超时设置: {timeout}")
    
    # 设置新的配置项
    config.set('api_version', 'v1.0')
    config.set('debug_mode', True)
    
    # 保存配置
    config.save('config_updated.yaml')
    print("✅ 配置已更新并保存")
    
    return config


def example_3_integration_with_existing_code():
    """示例3: 集成到现有代码"""
    print("\n=== 示例3: 集成到现有代码 ===")
    
    # 模拟你的现有代码结构
    class TaxiAgent:
        def __init__(self):
            # 从配置文件读取系统提示词
            self.system_prompt = get_main_process_system_prompt()
            print(f"✅ TaxiAgent初始化，系统提示词长度: {len(self.system_prompt)}")
        
        def get_system_prompt(self):
            return self.system_prompt
        
        def process_message(self, user_input: str):
            # 使用系统提示词处理消息
            print(f"📝 处理用户输入: {user_input}")
            print(f"🤖 使用系统提示词: {self.system_prompt[:50]}...")
            return f"基于系统提示词处理: {user_input}"
    
    # 创建agent实例
    agent = TaxiAgent()
    
    # 测试处理消息
    response = agent.process_message("我想从西湖打车到杭州东站")
    print(f"✅ 处理结果: {response}")
    
    return agent


def example_4_dynamic_config_reload():
    """示例4: 动态配置重载"""
    print("\n=== 示例4: 动态配置重载 ===")
    
    config = ConfigManager()
    
    # 获取初始配置
    initial_prompt = config.get_system_prompt()
    print(f"✅ 初始提示词长度: {len(initial_prompt)}")
    
    # 模拟配置文件更新（实际使用中可能是外部更新）
    print("🔄 模拟配置文件更新...")
    
    # 重新加载配置
    config.reload()
    updated_prompt = config.get_system_prompt()
    print(f"✅ 重载后提示词长度: {len(updated_prompt)}")
    
    return config


def example_5_error_handling():
    """示例5: 错误处理"""
    print("\n=== 示例5: 错误处理 ===")
    
    # 尝试读取不存在的配置文件
    prompt = get_main_process_system_prompt("nonexistent.yaml")
    if not prompt:
        print("❌ 配置文件不存在，使用默认提示词")
        default_prompt = "你是一个智能助手。"
        prompt = default_prompt
    
    print(f"✅ 最终使用的提示词: {prompt[:50]}...")
    
    # 使用配置管理器的错误处理
    try:
        config = ConfigManager("nonexistent.yaml")
        prompt2 = config.get_system_prompt()
        if not prompt2:
            print("❌ 配置管理器未获取到提示词")
    except Exception as e:
        print(f"❌ 配置管理器错误: {e}")


def main():
    """主函数，运行所有示例"""
    print("🚀 YAML配置工具使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_1_simple_usage()
    example_2_config_manager()
    example_3_integration_with_existing_code()
    example_4_dynamic_config_reload()
    example_5_error_handling()
    
    print("\n" + "=" * 50)
    print("✅ 所有示例运行完成！")
    
    print("\n📋 使用建议:")
    print("1. 简单场景：直接使用 get_main_process_system_prompt()")
    print("2. 复杂场景：使用 ConfigManager 类")
    print("3. 生产环境：添加适当的错误处理")
    print("4. 动态更新：使用 reload() 方法重新加载配置")


if __name__ == "__main__":
    main()
