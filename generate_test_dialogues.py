import json
import random
from datetime import datetime
import requests

BASE_URL = "http://localhost:5000"
DIALOGUES = []

def log_to_jsonl(user_input, system_response, api_request=None, api_response=None, error=None):
    # 原始数据
    raw_entry = {
        "timestamp": datetime.now().isoformat(),
        "user_input": user_input,
        "system_response": system_response,
        "api_request": api_request,
        "api_response": api_response,
        "error": error
    }
    
    # OpenAI兼容格式
    openai_entry = {
        "id": f"conv_{len(DIALOGUES)+1}",
        "created_at": datetime.now().isoformat(),
        "model": "taxi_assistant_v1",
        "temperature": 0.7,
        "usage_metrics": {
            "prompt_tokens": len(user_input) // 4,
            "completion_tokens": len(system_response) // 4,
            "total_tokens": (len(user_input) + len(system_response)) // 4
        },
        "messages": [
            {
                "role": "user",
                "content": user_input,
                "metadata": {
                    "api_request": api_request,
                    "timestamp": datetime.now().isoformat()
                }
            },
            {
                "role": "assistant",
                "content": system_response,
                "metadata": {
                    "api_response": api_response,
                    "error": error,
                    "timestamp": datetime.now().isoformat()
                }
            }
        ],
        "raw_data": raw_entry  # 保留原始数据
    }
    
    DIALOGUES.append(openai_entry)

def test_search_spots():
    # 场景1: 用户查询上车点
    user_input = "我在杭州市西湖区，附近有什么上车点吗？"
    
    try:
        # 系统调用API
        api_request = {
            "context": json.dumps({"req_id": "20001", "uid": "1913228968434073600", "coords": "120.12,30.28"}),
            "scriptId": "1000009",
            "subInteractiveName": "search_spot_by_coordinate",
            "scriptParams": json.dumps({"longitude": "120.12", "latitude": "30.28"})
        }
        response = requests.post(f"{BASE_URL}/search_spot_by_coordinate", json=api_request)
        api_response = response.json()
        
        # 系统生成回复
        spots = json.loads(api_response['data'])['list']
        system_response = f"附近有以下上车点:\n"
        for spot in spots[:3]:  # 只显示前3个
            system_response += f"- {spot['name']} (距离{spot['distance']}米)\n"
        
        log_to_jsonl(user_input, system_response, api_request, api_response)
        
    except Exception as e:
        log_to_jsonl(user_input, "抱歉，查询上车点时出错", api_request, None, str(e))

def test_estimate_price():
    # 场景2: 用户询问价格预估
    user_input = "从西湖到杭州东站打车要多少钱？"
    
    try:
        # 系统调用API
        api_request = {
            "context": json.dumps({"req_id": "20002", "uid": "1913228968434073601", "coords": "120.12,30.28"}),
            "scriptId": "1000009",
            "subInteractiveName": "get_estimate_price",
            "scriptParams": json.dumps({
                "from_longitude": "120.12",
                "from_latitude": "30.28",
                "to_longitude": "120.21",
                "to_latitude": "30.29",
                "car_type": "3"
            })
        }
        response = requests.post(f"{BASE_URL}/get_estimate_price", json=api_request)
        api_response = response.json()
        
        # 系统生成回复
        prices = json.loads(api_response['data'])['list']
        system_response = "预估价格:\n"
        for price in prices:
            system_response += f"- {price['name']}: {price['price']/100}元\n"
        
        log_to_jsonl(user_input, system_response, api_request, api_response)
        
    except Exception as e:
        log_to_jsonl(user_input, "抱歉，计算预估价格时出错", api_request, None, str(e))

def test_order_car():
    # 场景3: 用户叫车
    user_input = "我要从西湖叫车到杭州东站"
    
    try:
        # 系统调用API
        api_request = {
            "context": json.dumps({"req_id": "20003", "uid": "1913228968434073602", "coords": "120.12,30.28"}),
            "scriptId": "1000009",
            "subInteractiveName": "order_car",
            "scriptParams": json.dumps({
                "from_longitude": "120.12",
                "from_latitude": "30.28",
                "to_longitude": "120.21",
                "to_latitude": "30.29",
                "car_type": "3",
                "caller_phone": "13800138000"
            })
        }
        response = requests.post(f"{BASE_URL}/order_car", json=api_request)
        api_response = response.json()
        
        # 系统生成回复
        order_info = json.loads(api_response['data'])
        system_response = f"已为您叫车，订单号: {order_info['order_id']}\n"
        system_response += f"司机: {order_info['driver_info_vo']['name']} ({order_info['driver_info_vo']['car_type']})\n"
        system_response += f"预计到达时间: 5分钟内"
        
        log_to_jsonl(user_input, system_response, api_request, api_response)
        
    except Exception as e:
        log_to_jsonl(user_input, "抱歉，叫车时出错", api_request, None, str(e))

def test_error_scenario():
    # 场景4: 错误场景 - 无效坐标
    user_input = "我在火星能叫车吗？"
    
    try:
        # 系统调用API
        api_request = {
            "context": json.dumps({"req_id": "20004", "uid": "1913228968434073603", "coords": "0,0"}),
            "scriptId": "1000009",
            "subInteractiveName": "search_spot_by_coordinate",
            "scriptParams": json.dumps({"longitude": "0", "latitude": "0"})
        }
        response = requests.post(f"{BASE_URL}/search_spot_by_coordinate", json=api_request)
        api_response = response.json()
        
        # 这里应该会返回错误，但我们模拟系统处理
        system_response = "抱歉，该区域暂无服务覆盖"
        
        log_to_jsonl(user_input, system_response, api_request, api_response)
        
    except Exception as e:
        log_to_jsonl(user_input, "系统发生错误", api_request, None, str(e))

def save_dialogues():
    with open("test_dialogues.jsonl", "w", encoding="utf-8") as f:
        for dialogue in DIALOGUES:
            f.write(json.dumps(dialogue, ensure_ascii=False) + "\n")

if __name__ == "__main__":
    # 执行测试对话
    test_search_spots()
    test_estimate_price()
    test_order_car()
    test_error_scenario()
    
    # 保存结果
    save_dialogues()
    print(f"已生成 {len(DIALOGUES)} 条测试对话到 test_dialogues.jsonl")
