#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终演示脚本
展示完整的大模型集成、Function Calling、上下文管理和诊断系统
"""

import json
import uuid
import time
import random
from taxi_agent_system import EnhancedTaxiAgent
from test_mock_llm import MockLLMAgent


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def demo_function_calling_execution():
    """演示Function Calling执行"""
    print("=== Function Calling 执行演示 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试所有工具
    function_tests = [
        {
            "name": "call_taxi_service",
            "description": "打车服务",
            "args": {"start_place": "康德大厦", "end_place": "太阳宫", "car_prefer": "舒适型"},
            "expected": "成功调用打车服务"
        },
        {
            "name": "mcp_geocode_address", 
            "description": "地理编码",
            "args": {"address": "康德大厦", "city": "北京"},
            "expected": "返回地点坐标信息"
        },
        {
            "name": "mcp_get_city_code",
            "description": "城市代码查询",
            "args": {"city_name": "北京"},
            "expected": "返回城市代码信息"
        },
        {
            "name": "mcp_search_poi",
            "description": "POI搜索",
            "args": {"keyword": "星巴克", "city": "北京"},
            "expected": "返回POI搜索结果"
        }
    ]
    
    results = []
    
    for test in function_tests:
        print(f"\n--- 测试 {test['description']} ---")
        print(f"函数: {test['name']}")
        print(f"参数: {json.dumps(test['args'], ensure_ascii=False)}")
        print(f"预期: {test['expected']}")
        
        start_time = time.time()
        try:
            result = agent._execute_function(test['name'], test['args'], session_id)
            end_time = time.time()
            
            success = result.get("status", False)
            execution_time = end_time - start_time
            
            print(f"✅ 执行{'成功' if success else '失败'} (耗时: {execution_time:.2f}s)")
            
            if success:
                print(f"📄 结果摘要: {result.get('message', '无消息')}")
            else:
                print(f"❌ 错误: {result.get('error', '未知错误')}")
            
            results.append({
                "function": test['name'],
                "success": success,
                "execution_time": execution_time,
                "result": result
            })
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"❌ 异常: {e} (耗时: {execution_time:.2f}s)")
            
            # 记录错误到DebugAgent
            agent.debug_agent.log_error(str(e), {
                "function": test['name'],
                "args": test['args']
            })
            
            results.append({
                "function": test['name'],
                "success": False,
                "execution_time": execution_time,
                "error": str(e)
            })
    
    # 统计结果
    print(f"\n📊 执行统计:")
    successful = sum(1 for r in results if r['success'])
    total = len(results)
    success_rate = (successful / total) * 100
    avg_time = sum(r['execution_time'] for r in results) / total
    
    print(f"  成功率: {successful}/{total} ({success_rate:.1f}%)")
    print(f"  平均耗时: {avg_time:.2f}秒")
    
    return results, agent


def demo_context_management():
    """演示上下文管理"""
    print("\n=== 上下文管理演示 ===")
    
    agent = MockLLMAgent()  # 使用模拟Agent避免API依赖
    
    # 创建多个会话
    sessions = [
        {
            "id": generate_session_id(),
            "name": "商务用户",
            "conversations": [
                "你好，我需要打车服务",
                "我要从康德大厦去机场",
                "选择舒适型车辆",
                "大概需要多长时间？"
            ]
        },
        {
            "id": generate_session_id(),
            "name": "旅游用户", 
            "conversations": [
                "帮我查一下天安门的位置",
                "我想从天安门到故宫",
                "附近有什么好的餐厅吗？"
            ]
        }
    ]
    
    # 模拟交替对话
    max_turns = max(len(s["conversations"]) for s in sessions)
    
    for turn in range(max_turns):
        print(f"\n--- 轮次 {turn + 1} ---")
        
        for session in sessions:
            if turn < len(session["conversations"]):
                session_id = session["id"]
                user_name = session["name"]
                message = session["conversations"][turn]
                
                print(f"\n{user_name} ({session_id[:12]}...): {message}")
                
                try:
                    response = agent.process_message(message, session_id)
                    print(f"助手: {response}")
                    
                    # 显示上下文长度
                    if session_id in agent.context:
                        context_length = len(agent.context[session_id])
                        print(f"📝 上下文: {context_length} 条消息")
                    
                except Exception as e:
                    print(f"❌ 失败: {e}")
                
                time.sleep(0.3)
    
    # 上下文统计
    print(f"\n📊 上下文统计:")
    print(f"  管理会话数: {len(agent.context)}")
    total_messages = sum(len(context) for context in agent.context.values())
    print(f"  总消息数: {total_messages}")
    
    for session in sessions:
        session_id = session["id"]
        if session_id in agent.context:
            count = len(agent.context[session_id])
            print(f"  {session['name']}: {count} 条消息")
    
    return agent


def demo_debug_and_diagnosis(agent):
    """演示调试和诊断功能"""
    print("\n=== 调试和诊断演示 ===")
    
    # 模拟一些错误来展示诊断功能
    test_errors = [
        ("API密钥错误", "API_key authentication failed"),
        ("网络连接错误", "Network connection timeout"),
        ("参数错误", "Function parameter missing: start_place"),
        ("限流错误", "Rate limit exceeded for API calls"),
        ("数据格式错误", "JSON parse error in response")
    ]
    
    print("模拟错误场景:")
    for error_name, error_msg in test_errors:
        print(f"  记录错误: {error_name}")
        agent.debug_agent.log_error(error_msg, {
            "test_scenario": error_name,
            "timestamp": time.time()
        })
        time.sleep(0.1)
    
    # 获取诊断信息
    print(f"\n🔍 系统诊断:")
    diagnosis = agent.debug_agent.get_diagnosis()
    print(diagnosis)
    
    # 获取性能报告
    print(f"\n📊 性能报告:")
    # 模拟一些成功请求
    for _ in range(8):
        agent.debug_agent.log_success(random.uniform(0.5, 2.0))
    
    performance = agent.debug_agent.get_performance_report()
    for key, value in performance.items():
        print(f"  {key}: {value}")
    
    # 获取修正建议
    print(f"\n🔧 修正建议:")
    suggestions = agent.debug_agent.get_fix_suggestions()
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion}")
    
    return agent


def demo_random_session_support():
    """演示随机session_id支持"""
    print("\n=== 随机Session ID支持演示 ===")
    
    agent = MockLLMAgent()
    
    # 生成多个随机session
    sessions = []
    for i in range(5):
        session_id = generate_session_id()
        sessions.append({
            "id": session_id,
            "user": f"用户{i+1}",
            "message": f"这是用户{i+1}的测试消息"
        })
    
    print("创建的随机会话:")
    for session in sessions:
        print(f"  {session['user']}: {session['id']}")
    
    # 测试每个会话
    print(f"\n测试会话独立性:")
    for session in sessions:
        session_id = session["id"]
        user = session["user"]
        message = session["message"]
        
        try:
            response = agent.process_message(message, session_id)
            context_length = len(agent.context.get(session_id, []))
            print(f"  ✅ {user} ({session_id[:8]}...): {context_length} 条消息")
            
        except Exception as e:
            print(f"  ❌ {user}: 失败 - {e}")
    
    # 验证会话隔离
    print(f"\n验证会话隔离:")
    print(f"  总会话数: {len(agent.context)}")
    
    # 每个会话应该有独立的上下文
    unique_contexts = set()
    for session_id, context in agent.context.items():
        context_str = str(context)
        unique_contexts.add(context_str)
    
    if len(unique_contexts) == len(agent.context):
        print("  ✅ 会话完全隔离")
    else:
        print("  ⚠️  检测到会话上下文重叠")
    
    return sessions


def comprehensive_system_test():
    """综合系统测试"""
    print("\n=== 综合系统测试 ===")
    
    print("1. Function Calling 执行测试")
    results, agent = demo_function_calling_execution()
    
    print("\n2. 上下文管理测试")
    context_agent = demo_context_management()
    
    print("\n3. 调试诊断测试")
    debug_agent = demo_debug_and_diagnosis(agent)
    
    print("\n4. 随机Session支持测试")
    sessions = demo_random_session_support()
    
    # 综合评估
    print(f"\n📋 综合评估:")
    
    # Function Calling 成功率
    successful_functions = sum(1 for r in results if r['success'])
    function_success_rate = (successful_functions / len(results)) * 100
    print(f"  Function Calling 成功率: {function_success_rate:.1f}%")
    
    # 上下文管理
    total_contexts = len(context_agent.context)
    print(f"  上下文管理: {total_contexts} 个会话")
    
    # 错误处理
    error_count = len(debug_agent.debug_agent.error_log)
    print(f"  错误处理: 记录了 {error_count} 个错误")
    
    # Session 支持
    session_count = len(sessions)
    print(f"  Session 支持: 创建了 {session_count} 个随机会话")
    
    # 系统状态
    execution_history = len(debug_agent.state.execution_history)
    print(f"  执行历史: {execution_history} 条记录")
    
    print(f"\n✅ 系统测试完成")
    return {
        "function_success_rate": function_success_rate,
        "context_sessions": total_contexts,
        "error_records": error_count,
        "random_sessions": session_count,
        "execution_history": execution_history
    }


def main():
    """主演示函数"""
    print("增强版打车系统 - 最终演示")
    print("=" * 60)
    print("功能特性:")
    print("✅ 基于 StateManager 的状态管理")
    print("✅ 集成 amap_mcp_tools 高德地图工具")
    print("✅ 集成 call_taxi_service 打车服务")
    print("✅ Function Calling 支持")
    print("✅ 统一输出格式")
    print("✅ 随机 session_id 支持")
    print("✅ 上下文管理")
    print("✅ 增强调试诊断")
    print("=" * 60)
    
    # 运行综合测试
    test_results = comprehensive_system_test()
    
    print("\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    
    print(f"📊 测试结果:")
    for key, value in test_results.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎯 系统优势:")
    print("1. 模块化设计，易于扩展")
    print("2. 完善的错误处理和诊断")
    print("3. 支持多会话并发")
    print("4. 统一的输出格式")
    print("5. 基于大模型的智能交互")
    
    print(f"\n🔧 部署建议:")
    print("1. 配置 AMAP_API_KEY 启用地图功能")
    print("2. 配置 BAILIAN_API_KEY 启用AI对话")
    print("3. 监控系统性能和错误日志")
    print("4. 根据诊断建议进行优化")
    
    print(f"\n✨ 演示完成！系统已准备就绪。")


if __name__ == "__main__":
    main()
