<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚗</text></svg>" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#667eea" />
    <meta name="description" content="智能打车助手 - 基于高德地图API和AI技术的智能打车服务演示" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <title>智能打车助手</title>
    
    <style>
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      .loading-subtitle {
        font-size: 14px;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <noscript>您需要启用JavaScript才能运行此应用。</noscript>
    
    <!-- 加载动画 -->
    <div id="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">🚗 智能打车助手</div>
      <div class="loading-subtitle">正在加载中...</div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            loading.style.transition = 'opacity 0.5s ease';
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
