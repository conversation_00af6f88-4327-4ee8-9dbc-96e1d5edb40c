# 增强版打车系统

基于 StateManager 进行修改，集成了 amap_mcp_tools 和 call_taxi_service，支持 Function Calling 的增强版打车系统。

## 主要功能

### ✅ 已完成功能

1. **集成高德地图工具**
   - 地理编码：地点名称转经纬度坐标
   - 城市代码：获取城市的adcode和相关信息
   - POI搜索：搜索兴趣点（餐厅、酒店、景点等）

2. **集成打车服务**
   - 调用 `call_taxi_service` 函数
   - 统一输出格式
   - 支持车辆偏好设置

3. **Function Calling 支持**
   - 完整的工具定义
   - 参数验证
   - 错误处理

4. **StateManager 状态管理**
   - 执行历史记录
   - 错误日志
   - 状态跟踪

## 工具定义

### 1. mcp_geocode_address
- **功能**: 将地点名称转换为经纬度坐标
- **必需参数**: `address` (地点名称)
- **可选参数**: `city` (城市名称，提高精度)
- **示例**: `{"address": "康德大厦", "city": "北京"}`

### 2. mcp_get_city_code
- **功能**: 获取城市的adcode（城市ID）和相关信息
- **必需参数**: `city_name` (城市名称)
- **示例**: `{"city_name": "北京"}`

### 3. mcp_search_poi
- **功能**: 搜索POI（兴趣点）
- **必需参数**: `keyword` (搜索关键词)
- **可选参数**: `city` (城市名称), `types` (POI类型)
- **示例**: `{"keyword": "星巴克", "city": "北京"}`

### 4. call_taxi_service
- **功能**: 调用打车服务
- **必需参数**: `start_place` (出发地), `end_place` (目的地)
- **可选参数**: `car_prefer` (车辆偏好)
- **示例**: `{"start_place": "康德大厦", "end_place": "太阳", "car_prefer": "舒适型"}`

## 输出格式统一

### 原始 call_taxi_service 输出格式
```json
{
  "action_ret_msg": "康得大厦到太阳宫(地铁站)，可以么？",
  "status": 1,
  "err_msg": "",
  "err_code": "success",
  "detail_info": {},
  "params": {
    "output": "康得大厦到太阳宫(地铁站)，可以么？",
    "pickup_name": "康得大厦",
    "pickup_l": "116.304228",
    "pickup_r": "40.042815",
    "dest_name": "太阳宫(地铁站)",
    "dest_l": "116.448213",
    "dest_r": "39.973816",
    "sub_id": 2
  },
  "time_cost": 0.874
}
```

### 统一格式化后的输出
```json
{
  "status": true,
  "message": "康得大厦到太阳宫(地铁站)，可以么？",
  "error_code": "success",
  "error_message": "",
  "time_cost": 0.874,
  "details": {
    "pickup_name": "康得大厦",
    "pickup_location": {
      "longitude": "116.304228",
      "latitude": "40.042815"
    },
    "destination_name": "太阳宫(地铁站)",
    "destination_location": {
      "longitude": "116.448213",
      "latitude": "39.973816"
    },
    "sub_id": 2,
    "output": "康得大厦到太阳宫(地铁站)，可以么？"
  },
  "raw_data": { /* 原始数据用于调试 */ }
}
```

## 使用方法

### 1. 基本使用
```python
from taxi_agent_system import EnhancedTaxiAgent

# 创建增强版Agent
agent = EnhancedTaxiAgent()

# 处理用户消息（需要AI模型支持）
response = agent.process_message("我要从康德大厦打车到太阳宫", "session_1")
print(response)
```

### 2. 直接调用工具函数
```python
# 调用打车服务
result = agent._execute_function(
    "call_taxi_service",
    {
        "start_place": "康德大厦",
        "end_place": "太阳",
        "car_prefer": "舒适型"
    },
    "test_session"
)

# 调用高德地图工具
result = agent._execute_function(
    "mcp_geocode_address",
    {"address": "康德大厦", "city": "北京"},
    "test_session"
)
```

## 测试文件

1. **test_tools_only.py** - 工具函数测试（不依赖AI模型）
2. **enhanced_taxi_demo.py** - 完整演示程序
3. **test_enhanced_taxi_agent.py** - 完整功能测试

## 运行测试

```bash
# 测试工具函数
python test_tools_only.py

# 运行演示程序
python enhanced_taxi_demo.py

# 完整功能测试（需要API密钥）
python test_enhanced_taxi_agent.py
```

## API 配置要求

### 1. 高德地图API
- **环境变量**: `AMAP_API_KEY`
- **获取地址**: https://console.amap.com/
- **需要开通**: Web服务API

### 2. 百炼AI模型
- **环境变量**: `BAILIAN_API_KEY`
- **用途**: AI对话和Function Calling

### 3. 打车服务
- 已集成 `langflow_api_V4`
- 无需额外配置

## 主要改进

1. **统一输出格式**: 将原始的复杂格式统一为标准化的JSON结构
2. **Function Calling支持**: 完整的工具定义和参数验证
3. **错误处理**: 完善的错误处理和日志记录
4. **状态管理**: 基于StateManager的状态跟踪
5. **模块化设计**: 清晰的模块分离和接口定义

## 文件结构

```
taxi_demo/
├── taxi_agent_system.py          # 主要系统文件
├── amap_mcp_tools.py             # 高德地图工具
├── langflow_api_V4.py            # 打车服务API
├── test_tools_only.py            # 工具测试
├── enhanced_taxi_demo.py         # 演示程序
├── test_enhanced_taxi_agent.py   # 完整测试
└── README_Enhanced_System.md     # 本文档
```

## 测试结果

✅ Function Calling工具定义正确
✅ 高德地图工具集成成功
✅ 打车服务调用正常
✅ 输出格式统一化完成
✅ 错误处理和状态管理正常

## 下一步计划

1. 配置API密钥进行完整测试
2. 优化错误处理机制
3. 添加更多工具支持
4. 完善文档和示例
