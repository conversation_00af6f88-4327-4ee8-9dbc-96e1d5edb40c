#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为模拟器测试脚本
验证系统功能是否正常工作
"""

import os
import sys
import traceback
from user_behavior_simulator import UserBehaviorSimulator


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        # 初始化模拟器
        simulator = UserBehaviorSimulator()
        print("✅ 模拟器初始化成功")
        
        # 测试会话创建
        session_id = simulator.generate_session_id()
        print(f"✅ 会话ID生成成功: {session_id[:8]}...")
        
        # 测试场景分配
        simulator.assign_scenario_to_session(session_id, 1)
        print("✅ 场景分配成功")
        
        # 测试用户输入生成
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"✅ 用户输入生成成功: {user_input}")
        
        # 测试会话信息获取
        session_info = simulator.get_session_info(session_id)
        print(f"✅ 会话信息获取成功: {session_info['scenario_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False


def test_all_scenarios():
    """测试所有用户场景"""
    print("\n=== 测试所有用户场景 ===")
    
    simulator = UserBehaviorSimulator()
    success_count = 0
    
    for scenario_type in range(1, 6):
        try:
            session_id = simulator.generate_session_id()
            simulator.assign_scenario_to_session(session_id, scenario_type)
            
            scenario_name = simulator.user_scenarios[scenario_type]['name']
            user_input = simulator.generate_user_input(session_id, "", 1)
            
            print(f"✅ 场景{scenario_type} ({scenario_name}): {user_input}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 场景{scenario_type}测试失败: {e}")
    
    print(f"\n场景测试结果: {success_count}/5 成功")
    return success_count == 5


def test_conversation_simulation():
    """测试对话模拟"""
    print("\n=== 测试对话模拟 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, 1)  # 首次使用用户
        
        print("开始模拟对话...")
        conversation = simulator.simulate_conversation(session_id, max_turns=2)
        
        if conversation:
            print(f"✅ 对话模拟成功，共{len(conversation)}轮")
            for i, turn in enumerate(conversation, 1):
                print(f"  第{i}轮 - 用户: {turn['user_input'][:30]}...")
                print(f"        系统: {turn['system_response'][:30]}...")
        else:
            print("❌ 对话模拟失败：无对话记录")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 对话模拟测试失败: {e}")
        traceback.print_exc()
        return False


def test_batch_generation():
    """测试批量生成"""
    print("\n=== 测试批量生成 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        print("生成测试场景...")
        test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=1)
        
        if test_scenarios:
            print(f"✅ 批量生成成功，共{len(test_scenarios)}个场景")
            
            # 测试分析功能
            analysis = simulator.analyze_test_results(test_scenarios)
            print(f"✅ 结果分析成功，成功率: {analysis['overall_stats']['success_rate']:.1%}")
            
            # 测试保存功能
            filename = simulator.save_test_results(test_scenarios)
            print(f"✅ 结果保存成功: {filename}")
            
            return True
        else:
            print("❌ 批量生成失败：无测试场景")
            return False
        
    except Exception as e:
        print(f"❌ 批量生成测试失败: {e}")
        traceback.print_exc()
        return False


def test_environment():
    """测试环境配置"""
    print("=== 环境配置检查 ===")
    
    # 检查必需的模块
    required_modules = ['openai', 'json', 'uuid', 'datetime']
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} 模块可用")
        except ImportError:
            print(f"❌ {module} 模块缺失")
    
    # 检查环境变量
    bailian_key = os.getenv("BAILIAN_API_KEY")
    amap_key = os.getenv("AMAP_API_KEY")
    
    if bailian_key:
        print(f"✅ BAILIAN_API_KEY 已设置 ({bailian_key[:10]}...)")
    else:
        print("⚠️  BAILIAN_API_KEY 未设置，将使用回退模式")
    
    if amap_key:
        print(f"✅ AMAP_API_KEY 已设置 ({amap_key[:10]}...)")
    else:
        print("⚠️  AMAP_API_KEY 未设置，地图功能可能不可用")
    
    # 检查依赖文件
    required_files = ['taxi_agent_system.py', 'user_behavior_simulator.py']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 文件存在")
        else:
            print(f"❌ {file} 文件缺失")
            return False
    
    return True


def run_comprehensive_test():
    """运行综合测试"""
    print("🚗 用户行为模拟器综合测试")
    print("=" * 50)
    
    test_results = []
    
    # 环境检查
    test_results.append(("环境配置", test_environment()))
    
    # 基本功能测试
    test_results.append(("基本功能", test_basic_functionality()))
    
    # 场景测试
    test_results.append(("用户场景", test_all_scenarios()))
    
    # 对话模拟测试
    test_results.append(("对话模拟", test_conversation_simulation()))
    
    # 批量生成测试
    test_results.append(("批量生成", test_batch_generation()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
        return False


def quick_demo():
    """快速演示"""
    print("\n=== 快速演示 ===")
    
    try:
        simulator = UserBehaviorSimulator()
        
        # 演示一个简单的对话
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, 1)
        
        print("演示首次使用用户的对话:")
        
        # 第一轮
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"\n用户: {user_input}")
        
        try:
            system_response = simulator.taxi_agent.process_user_input(user_input)
            print(f"系统: {system_response}")
        except Exception as e:
            print(f"系统: [模拟回复] 您好！我可以帮您叫车。请告诉我您的出发地和目的地。")
        
        print("\n演示完成！")
        
    except Exception as e:
        print(f"演示失败: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        quick_demo()
    else:
        success = run_comprehensive_test()
        
        if success:
            print("\n要运行快速演示，请执行:")
            print("python test_user_simulator.py --demo")
            print("\n要运行完整演示，请执行:")
            print("python demo_user_simulator.py")
        
        sys.exit(0 if success else 1)
