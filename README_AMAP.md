# 高德地图MCP工具集

这是一个集成高德地图API的Python工具包，主要提供两个核心功能：
1. **地点名称转经纬度坐标**
2. **地点名称转城市ID**

## 功能特性

### 核心功能
- 🗺️ **地理编码**: 将地点名称转换为精确的经纬度坐标
- 🏙️ **城市查询**: 获取城市的adcode（城市ID）和相关信息
- 📍 **POI搜索**: 搜索兴趣点（如商店、餐厅、景点等）
- 🔄 **批量处理**: 支持批量地理编码
- 🛠️ **MCP集成**: 提供function calling接口，可直接集成到AI系统

### 技术特性
- ✅ 完整的错误处理和重试机制
- ✅ 支持环境变量配置
- ✅ 详细的API响应解析
- ✅ 类型提示支持
- ✅ 可扩展的架构设计

## 安装和配置

### 1. 安装依赖
```bash
pip install requests
```

### 2. 获取高德地图API密钥
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取API Key

### 3. 配置环境变量
```bash
# 设置高德地图API密钥
export AMAP_API_KEY="your_amap_api_key_here"

# 如果使用百炼模型，还需要设置
export BAILIAN_API_KEY="your_bailian_api_key_here"
```

或者在Python代码中设置：
```python
import os
os.environ['AMAP_API_KEY'] = 'your_amap_api_key_here'
```

## 使用方法

### 基础使用

```python
from amap_mcp_tools import AmapMCPTools

# 初始化工具
amap = AmapMCPTools()

# 1. 地点转经纬度
result = amap.geocode_address("西湖", "杭州")
if result["status"]:
    data = result["data"]
    print(f"经度: {data['longitude']}")
    print(f"纬度: {data['latitude']}")
    print(f"完整地址: {data['formatted_address']}")

# 2. 获取城市ID
result = amap.get_city_code("杭州")
if result["status"]:
    data = result["data"]
    print(f"城市代码: {data['city_code']}")
    print(f"行政区代码: {data['adcode']}")

# 3. POI搜索
result = amap.search_poi("星巴克", "杭州")
if result["status"]:
    pois = result["data"]["pois"]
    for poi in pois[:5]:  # 显示前5个结果
        print(f"{poi['name']} - {poi['address']}")
```

### MCP函数调用

```python
from amap_mcp_tools import mcp_geocode_address, mcp_get_city_code

# 直接调用MCP函数
result = mcp_geocode_address("天安门", "北京")
print(result)

result = mcp_get_city_code("北京")
print(result)
```

### 集成到AI系统

```python
from taxi_agent_with_amap import EnhancedTaxiAgent

# 创建增强版打车助手
agent = EnhancedTaxiAgent()

# 处理用户请求
response = agent.process_message("我想从西湖打车到杭州东站，大概多少钱？")
print(response)
```

## API响应格式

### 地理编码响应
```json
{
  "status": true,
  "data": {
    "longitude": 120.153576,
    "latitude": 30.287459,
    "formatted_address": "浙江省杭州市西湖区西湖",
    "province": "浙江省",
    "city": "杭州市",
    "district": "西湖区",
    "adcode": "330106"
  }
}
```

### 城市代码响应
```json
{
  "status": true,
  "data": {
    "city_code": "0571",
    "adcode": "330100",
    "city_name": "杭州市",
    "province": "浙江省",
    "center": {
      "longitude": 120.153576,
      "latitude": 30.287459
    }
  }
}
```

### 错误响应
```json
{
  "status": false,
  "error": "未找到地点: 不存在的地点"
}
```

## 文件说明

- `amap_mcp_tools.py` - 核心工具类和MCP函数
- `amap_config.py` - 配置管理
- `taxi_agent_with_amap.py` - 集成示例（打车系统）
- `test_amap_integration.py` - 完整测试套件
- `README_AMAP.md` - 使用说明

## 测试

运行完整测试：
```bash
python test_amap_integration.py
```

测试包括：
- ✅ 基础功能测试
- ✅ MCP函数接口测试  
- ✅ 集成场景测试
- ✅ 交互式测试

## Function Calling 工具定义

系统提供了完整的function calling工具定义，可直接用于AI模型：

```python
from amap_mcp_tools import AMAP_TOOLS

# AMAP_TOOLS 包含以下工具：
# - mcp_geocode_address: 地点转经纬度
# - mcp_get_city_code: 获取城市ID  
# - mcp_search_poi: POI搜索
```

## 常见问题

### Q: API密钥无效
A: 请检查：
1. API密钥是否正确
2. 是否已在高德控制台启用相关服务
3. 环境变量是否正确设置

### Q: 搜索结果不准确
A: 建议：
1. 提供更具体的地点名称
2. 指定城市参数提高精度
3. 使用完整地址而非简称

### Q: 请求频率限制
A: 高德API有频率限制，请：
1. 检查API套餐限制
2. 实现请求缓存
3. 添加请求间隔

## 扩展功能

工具支持扩展更多高德API功能：
- 逆地理编码（坐标转地址）
- 路径规划
- 距离计算
- 实时交通信息

## 许可证

本项目仅供学习和开发使用，请遵守高德地图API使用条款。
