"""
集成高德地图工具的打车系统
在原有系统基础上添加高德地图MCP工具支持
"""

import json
import os
from typing import Dict, List, Optional
from datetime import datetime
from openai import OpenAI

# 导入高德地图工具
from amap_mcp_tools import (
    mcp_geocode_address, 
    mcp_get_city_code, 
    mcp_search_poi,
    AMAP_TOOLS
)


class EnhancedTaxiAgent:
    """增强版打车Agent，集成高德地图功能"""
    
    def __init__(self):
        # 百炼模型配置
        self.bailian_client = OpenAI(
            api_key=os.getenv("BAILIAN_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        self.bailian_model_name = "qwen-max"
        
        # 对话上下文
        self.context = {}
        
        # 系统提示词
        self.system_prompt = """你是一个智能打车助手，具备以下能力：

1. 地理位置处理：
   - 将地点名称转换为精确的经纬度坐标
   - 获取城市的ID和相关信息
   - 搜索附近的兴趣点（POI）

2. 打车服务：
   - 查找上车点
   - 估算打车价格
   - 协助叫车服务

当用户提到地点名称时，你应该：
1. 首先使用高德地图工具获取准确的经纬度坐标
2. 然后基于坐标提供相应的打车服务

请用友好、专业的语气与用户交流。"""
        
        # 合并工具定义
        self.tools = AMAP_TOOLS + [
            {
                "type": "function",
                "function": {
                    "name": "search_taxi_spots",
                    "description": "根据经纬度坐标搜索附近的上车点",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "longitude": {"type": "number", "description": "经度"},
                            "latitude": {"type": "number", "description": "纬度"},
                            "radius": {"type": "number", "description": "搜索半径（米），默认500"}
                        },
                        "required": ["longitude", "latitude"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "estimate_taxi_price",
                    "description": "估算两点间的打车价格",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "from_longitude": {"type": "number", "description": "出发地经度"},
                            "from_latitude": {"type": "number", "description": "出发地纬度"},
                            "to_longitude": {"type": "number", "description": "目的地经度"},
                            "to_latitude": {"type": "number", "description": "目的地纬度"}
                        },
                        "required": ["from_longitude", "from_latitude", "to_longitude", "to_latitude"]
                    }
                }
            }
        ]
    
    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息"""
        try:
            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]
            
            # 添加用户消息
            self.context[session_id].append({"role": "user", "content": user_input})
            
            # 调用百炼API
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                tool_choice="auto"
            )
            
            response_message = response.choices[0].message
            
            # 检查是否需要function calling
            if response_message.tool_calls:
                # 添加助手消息到上下文
                self.context[session_id].append({
                    "role": "assistant",
                    "content": response_message.content,
                    "tool_calls": response_message.tool_calls
                })
                
                # 处理每个function call
                for tool_call in response_message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
                    
                    # 执行function
                    function_response = self._execute_function(function_name, function_args)
                    
                    # 将结果添加到上下文
                    self.context[session_id].append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": function_name,
                        "content": json.dumps(function_response, ensure_ascii=False)
                    })
                
                # 获取最终回复
                final_response = self.bailian_client.chat.completions.create(
                    model=self.bailian_model_name,
                    messages=self.context[session_id]
                )
                
                final_message = final_response.choices[0].message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })
                
                return final_message
            else:
                # 直接返回响应
                self.context[session_id].append({
                    "role": "assistant",
                    "content": response_message.content
                })
                return response_message.content
                
        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            print(f"Error: {error_msg}")
            return error_msg
    
    def _execute_function(self, function_name: str, args: dict) -> dict:
        """执行function调用"""
        try:
            if function_name == "mcp_geocode_address":
                return mcp_geocode_address(**args)
            elif function_name == "mcp_get_city_code":
                return mcp_get_city_code(**args)
            elif function_name == "mcp_search_poi":
                return mcp_search_poi(**args)
            elif function_name == "search_taxi_spots":
                return self._search_taxi_spots(**args)
            elif function_name == "estimate_taxi_price":
                return self._estimate_taxi_price(**args)
            else:
                return {
                    "status": False,
                    "error": f"未知的函数: {function_name}"
                }
        except Exception as e:
            return {
                "status": False,
                "error": f"执行函数 {function_name} 时出错: {str(e)}"
            }
    
    def _search_taxi_spots(self, longitude: float, latitude: float, radius: int = 500) -> dict:
        """搜索上车点（模拟实现）"""
        # 这里应该调用实际的打车API
        return {
            "status": True,
            "data": {
                "spots": [
                    {
                        "name": f"上车点A（距离{longitude:.4f},{latitude:.4f}）",
                        "distance": 100,
                        "longitude": longitude + 0.001,
                        "latitude": latitude + 0.001
                    },
                    {
                        "name": f"上车点B（距离{longitude:.4f},{latitude:.4f}）",
                        "distance": 200,
                        "longitude": longitude - 0.001,
                        "latitude": latitude - 0.001
                    }
                ]
            }
        }
    
    def _estimate_taxi_price(self, from_longitude: float, from_latitude: float, 
                           to_longitude: float, to_latitude: float) -> dict:
        """估算打车价格（模拟实现）"""
        # 简单的距离计算和价格估算
        import math
        
        # 计算大致距离（公里）
        lat_diff = to_latitude - from_latitude
        lon_diff = to_longitude - from_longitude
        distance_km = math.sqrt(lat_diff**2 + lon_diff**2) * 111  # 粗略转换为公里
        
        # 模拟价格计算
        base_price = 10  # 起步价
        price_per_km = 2.5  # 每公里价格
        estimated_price = base_price + distance_km * price_per_km
        
        return {
            "status": True,
            "data": {
                "distance_km": round(distance_km, 2),
                "estimated_price": round(estimated_price, 2),
                "currency": "CNY",
                "car_types": [
                    {"type": "经济型", "price": round(estimated_price, 2)},
                    {"type": "舒适型", "price": round(estimated_price * 1.3, 2)},
                    {"type": "豪华型", "price": round(estimated_price * 1.8, 2)}
                ]
            }
        }


def main():
    """主函数，用于测试"""
    agent = EnhancedTaxiAgent()
    
    print("=== 增强版打车助手 ===")
    print("支持功能：")
    print("1. 地点名称转经纬度")
    print("2. 获取城市ID")
    print("3. POI搜索")
    print("4. 打车服务")
    print("输入 'quit' 退出\n")
    
    session_id = "test_session"
    
    while True:
        user_input = input("用户: ").strip()
        if user_input.lower() == 'quit':
            break
        
        if user_input:
            response = agent.process_message(user_input, session_id)
            print(f"助手: {response}\n")


if __name__ == "__main__":
    main()
