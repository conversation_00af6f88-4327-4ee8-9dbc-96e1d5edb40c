"""
重构的智能生活助手系统
主要功能：生活陪伴助手，目前支持打车服务
"""

from typing import Dict, List, Optional
from datetime import datetime
import json
import requests
import os
from openai import OpenAI
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi, AMAP_TOOLS
from langflow_api_V4 import call_taxi_service
import time

class StateManager:
    """全局状态管理器"""
    def __init__(self):
        self.conversation_state = {}
        self.api_results = {}
        self.missing_params = {}
        self.execution_history = []
        self.current_status = {
            'last_action': None,
            'status': 'ready',
            'error': None
        }
        
    def log_execution(self, action: str, status: str, details: Dict):
        """记录执行历史"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.execution_history.append(entry)
        self.current_status = {
            'last_action': action,
            'status': status,
            'error': details.get('error')
        }
        
    def update_state(self, key: str, value: any):
        self.conversation_state[key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }

class DebugAgent:
    """重构的Debug Agent - 4步工作流程"""
    def __init__(self, state_manager: StateManager, bailian_client=None):
        self.state = state_manager
        self.bailian_client = bailian_client
        self.error_log = []
        self.performance_metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0
        }
    
    def step1_pre_validation(self, function_name: str, args: dict, user_intent: str) -> dict:
        """步骤1: 函数调用前的前置验证"""
        validation_result = {
            "is_valid": True,
            "missing_params": [],
            "error_code": 1,  # 1: 完全成功, 0.5: 成功但有警告, 0: 缺少参数, -1: 函数解析错误
            "issues": []
        }
        
        # 检查函数是否存在
        available_functions = ["mcp_geocode_address", "mcp_get_city_code", "mcp_search_poi", "call_taxi_service"]
        if function_name not in available_functions:
            validation_result.update({
                "is_valid": False,
                "error_code": -1,
                "issues": [f"未知函数: {function_name}"]
            })
            return validation_result
        
        # 检查必需参数
        required_params = self._get_required_params(function_name)
        missing = []
        
        for param in required_params:
            if param not in args or not args[param] or str(args[param]).strip() == "":
                missing.append(param)
        
        if missing:
            validation_result.update({
                "is_valid": False,
                "missing_params": missing,
                "error_code": 0,
                "issues": [f"缺少必需参数: {', '.join(missing)}"]
            })
        
        # 检查参数合理性
        reasonableness_check = self._check_parameter_reasonableness(function_name, args, user_intent)
        if not reasonableness_check["is_reasonable"]:
            validation_result.update({
                "is_valid": False,
                "error_code": 0,
                "issues": reasonableness_check["issues"]
            })
        
        return validation_result
    
    def step2_parameter_completion(self, function_name: str, missing_params: list, user_intent: str) -> dict:
        """步骤2: 参数补全 - 生成澄清问题"""
        if not self.bailian_client or not missing_params:
            return self._get_fallback_clarification(function_name, missing_params)
        
        try:
            # 生成针对性的澄清问题
            prompt = f"""用户想要使用{function_name}功能，但缺少以下参数：{', '.join(missing_params)}
用户原始意图：{user_intent}

请生成一个简洁的澄清问题来获取缺失信息。要求：
1. 问题要简短明了，适合语音输出，不超过20字
2. 一次只问最关键的1个参数
3. 提供具体的例子帮助用户理解
4. 语气友好自然

只返回问题内容，不要其他解释。"""

            response = self.bailian_client.chat.completions.create(
                model="qwen-max",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=50,
                temperature=0.3
            )
            
            question = response.choices[0].message.content.strip()
            
            return {
                "clarification_needed": True,
                "question": question,
                "missing_params": missing_params[:1]  # 一次只问一个参数
            }
            
        except Exception as e:
            return self._get_fallback_clarification(function_name, missing_params)
    
    def step3_execute_function(self, function_name: str, args: dict) -> dict:
        """步骤3: 执行函数调用"""
        return {"status": "ready_to_execute", "function_name": function_name, "args": args}
    
    def step4_result_analysis(self, function_name: str, args: dict, result: dict, user_intent: str) -> dict:
        """步骤4: 结果分析和候选项提供"""
        analysis = {
            "success": result.get("status", False),
            "candidates": [],
            "suggestions": [],
            "need_clarification": False,
            "brief_response": ""
        }
        
        if not result.get("status", False):
            # 执行失败，提供错误分析和建议
            analysis["suggestions"] = self._analyze_failure_and_suggest(function_name, args, result, user_intent)
            analysis["brief_response"] = "抱歉，查询失败了。"
            return analysis
        
        # 成功执行，分析结果并提供候选项
        if function_name == "mcp_search_poi":
            analysis["candidates"] = self._extract_poi_candidates(result)
            if len(analysis["candidates"]) > 1:
                analysis["brief_response"] = f"找到{len(analysis['candidates'])}个相关地点"
            else:
                analysis["brief_response"] = "已找到位置"
                
        elif function_name == "call_taxi_service":
            analysis["suggestions"] = self._analyze_taxi_result(result, user_intent)
            analysis["brief_response"] = "打车已安排"
            
        elif function_name == "mcp_geocode_address":
            analysis["brief_response"] = "位置已找到"
            
        elif function_name == "mcp_get_city_code":
            analysis["brief_response"] = "城市信息已获取"
        
        # 常识性检查
        reasonableness = self._check_result_reasonableness(function_name, args, result, user_intent)
        if not reasonableness["is_reasonable"]:
            analysis["need_clarification"] = True
            analysis["suggestions"].extend(reasonableness["suggestions"])
            analysis["brief_response"] = "结果可能有问题，请确认"
        
        return analysis
    
    def handle_out_of_scope(self, user_request: str) -> dict:
        """处理超出范围的请求"""
        # 基础范围分析
        scope_analysis = self._basic_scope_analysis(user_request)
        
        if "超出" in scope_analysis:
            # 触发联网搜索或提供建议
            if self.bailian_client:
                return self._provide_intelligent_suggestions(user_request)
            else:
                return {
                    "response": self._basic_suggestions(user_request),
                    "enable_search": True
                }
        
        return {"response": "我可以帮您查询地点、搜索POI或安排打车服务。"}
    
    def _get_required_params(self, function_name: str) -> list:
        """获取函数的必需参数"""
        param_map = {
            "mcp_geocode_address": ["address"],
            "mcp_get_city_code": ["city_name"],
            "mcp_search_poi": ["keyword"],
            "call_taxi_service": ["start_place", "end_place"]
        }
        return param_map.get(function_name, [])
    
    def _check_parameter_reasonableness(self, function_name: str, args: dict, user_intent: str) -> dict:
        """检查参数合理性"""
        result = {"is_reasonable": True, "issues": []}
        
        if function_name == "call_taxi_service":
            start = args.get("start_place", "").strip()
            end = args.get("end_place", "").strip()
            
            # 检查起点终点是否相同
            if start and end and start.lower() == end.lower():
                result["is_reasonable"] = False
                result["issues"].append("起点和终点不能相同")
            
            # 检查是否为明显不合理的地点
            unreasonable_places = ["火星", "月球", "外太空", "地狱", "天堂"]
            for place in [start, end]:
                if any(unreasonable in place for unreasonable in unreasonable_places):
                    result["is_reasonable"] = False
                    result["issues"].append(f"地点'{place}'不是有效的地理位置")
        
        return result
    
    def _get_fallback_clarification(self, function_name: str, missing_params: list) -> dict:
        """获取回退澄清问题"""
        questions = {
            "mcp_geocode_address": {"address": "请告诉我具体地点？"},
            "mcp_get_city_code": {"city_name": "请告诉我城市名称？"},
            "mcp_search_poi": {"keyword": "请告诉我要搜索什么？"},
            "call_taxi_service": {
                "start_place": "请告诉我出发地点？",
                "end_place": "请告诉我目的地？"
            }
        }
        
        function_questions = questions.get(function_name, {})
        if missing_params and missing_params[0] in function_questions:
            return {
                "clarification_needed": True,
                "question": function_questions[missing_params[0]],
                "missing_params": missing_params[:1]
            }
        
        return {"clarification_needed": False, "question": ""}
    
    def _extract_poi_candidates(self, result: dict) -> list:
        """从POI搜索结果中提取候选项"""
        candidates = []
        if result.get("status") and "data" in result:
            pois = result["data"].get("pois", [])
            for poi in pois[:3]:  # 最多3个候选，保持简洁
                candidates.append({
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", "")
                })
        return candidates
    
    def _analyze_taxi_result(self, result: dict, user_intent: str) -> list:
        """分析打车结果"""
        suggestions = []
        if result.get("status"):
            data = result.get("data", {})
            if "estimated_price" in data:
                suggestions.append(f"约{data['estimated_price']}元")
            if "distance_km" in data:
                suggestions.append(f"{data['distance_km']}公里")
        return suggestions
    
    def _check_result_reasonableness(self, function_name: str, args: dict, result: dict, user_intent: str) -> dict:
        """检查结果合理性"""
        return {"is_reasonable": True, "suggestions": []}
    
    def _analyze_failure_and_suggest(self, function_name: str, args: dict, result: dict, user_intent: str) -> list:
        """分析失败原因并提供建议"""
        return ["请检查输入信息是否正确"]
    
    def _basic_scope_analysis(self, request: str) -> str:
        """基础的范围分析"""
        request_lower = request.lower()
        
        if any(keyword in request_lower for keyword in ["天气", "气温", "下雨"]):
            return "天气查询 - 超出服务范围"
        elif any(keyword in request_lower for keyword in ["电影", "音乐", "娱乐"]):
            return "娱乐推荐 - 超出服务范围"
        elif any(keyword in request_lower for keyword in ["新闻", "股价", "财经"]):
            return "新闻财经 - 超出服务范围"
        else:
            return "可能在服务范围内"
    
    def _provide_intelligent_suggestions(self, request: str) -> dict:
        """使用大模型提供智能建议"""
        try:
            prompt = f"""用户请求：{request}

这个请求超出了我的专业服务范围（地点查询、POI搜索、打车服务）。
请提供一个简洁的回复（不超过30字），告诉用户：
1. 我目前不支持这个功能
2. 简单建议他们使用什么工具或平台
3. 语气友好

只返回回复内容，不要其他解释。"""

            response = self.bailian_client.chat.completions.create(
                model="qwen-max",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=60,
                temperature=0.3
            )
            
            return {
                "response": response.choices[0].message.content.strip(),
                "enable_search": True
            }
            
        except Exception as e:
            return {
                "response": self._basic_suggestions(request),
                "enable_search": True
            }
    
    def _basic_suggestions(self, request: str) -> str:
        """基础建议"""
        analysis = self._basic_scope_analysis(request)
        
        if "天气查询" in analysis:
            return "建议使用天气APP查询天气信息"
        elif "娱乐推荐" in analysis:
            return "建议使用豆瓣等平台获取推荐"
        elif "新闻财经" in analysis:
            return "建议使用专业财经APP查询"
        else:
            return "我专注于地点查询和打车服务"


class EnhancedLifeAssistant:
    """智能生活陪伴助手 - 目前支持打车服务"""
    def __init__(self):
        self.state = StateManager()

        # 百炼模型配置
        api_key = os.getenv("BAILIAN_API_KEY")
        if api_key:
            self.bailian_client = OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            self.bailian_model_name = "qwen-max"
        else:
            print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能将不可用")
            self.bailian_client = None
            self.bailian_model_name = None

        # 初始化Debug Agent
        self.debug_agent = DebugAgent(self.state, self.bailian_client)

        # 初始化高德地图工具
        try:
            self.amap_tools = AmapMCPTools()
        except Exception as e:
            print(f"警告: 高德地图工具初始化失败: {e}")
            self.amap_tools = None

        # 更新系统提示词 - 生活陪伴助手
        self.system_prompt = """你是一个智能生活陪伴助手，致力于让用户的生活更便捷。

目前我支持的核心功能：
1. 🗺️ 地点查询：将地点名称转换为精确坐标
2. 🏙️ 城市信息：获取城市代码和相关信息
3. 📍 POI搜索：搜索餐厅、酒店、景点等兴趣点
4. 🚗 打车服务：智能安排出行，估算费用

交互原则：
- 回复要简洁明了，适合语音输出
- 优先询问最关键的信息
- 对于超出能力范围的请求，诚实说明并提供建议
- 始终保持友好和耐心的语气

请根据用户需求选择合适的工具帮助用户。"""

        self.context = {}  # 存储对话上下文

        # 定义可用的工具
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "mcp_geocode_address",
                    "description": "将地点名称转换为经纬度坐标",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "address": {
                                "type": "string",
                                "description": "地点名称或地址"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选"
                            }
                        },
                        "required": ["address"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_get_city_code",
                    "description": "获取城市的代码和信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city_name": {
                                "type": "string",
                                "description": "城市名称"
                            }
                        },
                        "required": ["city_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_search_poi",
                    "description": "搜索兴趣点，如餐厅、酒店、景点等",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "keyword": {
                                "type": "string",
                                "description": "搜索关键词"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选"
                            },
                            "types": {
                                "type": "string",
                                "description": "POI类型，可选"
                            }
                        },
                        "required": ["keyword"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "call_taxi_service",
                    "description": "调用打车服务，安排车辆出行",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "start_place": {
                                "type": "string",
                                "description": "出发地点"
                            },
                            "end_place": {
                                "type": "string",
                                "description": "目的地"
                            },
                            "car_prefer": {
                                "type": "string",
                                "description": "车辆偏好，可选"
                            }
                        },
                        "required": ["start_place", "end_place"]
                    }
                }
            }
        ]

    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息的主要方法 - 新的4步流程"""
        try:
            # 记录开始状态
            self.state.log_execution(
                action="process_message",
                status="processing",
                details={"user_input": user_input, "session_id": session_id}
            )

            # 检查是否有可用的AI客户端
            if not self.bailian_client:
                return "抱歉，AI功能暂不可用。请设置BAILIAN_API_KEY环境变量。"

            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]

            # 添加用户消息
            self.context[session_id].append({"role": "user", "content": user_input})

            # 调用百炼API获取响应
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                tool_choice="auto"
            )

            response_message = response.choices[0].message
            self.context[session_id].append(response_message)

            # 检查是否需要function calling
            if response_message.tool_calls:
                return self._handle_function_calls(response_message.tool_calls, user_input, session_id)
            else:
                # 没有function call，检查是否超出范围
                if self._is_out_of_scope(user_input):
                    out_of_scope_result = self.debug_agent.handle_out_of_scope(user_input)
                    return out_of_scope_result["response"]

                # 直接返回响应并优化简洁性
                final_message = self._optimize_response_brevity(response_message.content)
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                return final_message

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            self.state.log_execution(
                action="process_message",
                status="failed",
                details={"error": str(e)}
            )
            return "抱歉，出现了一些问题，请稍后再试。"

    def _handle_function_calls(self, tool_calls, user_input: str, session_id: str) -> str:
        """处理函数调用 - 新的4步流程"""
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            try:
                function_args = json.loads(tool_call.function.arguments)
            except json.JSONDecodeError:
                return "抱歉，参数解析失败，请重新描述您的需求。"

            # 步骤1: 前置验证
            validation_result = self.debug_agent.step1_pre_validation(
                function_name, function_args, user_input
            )

            if not validation_result["is_valid"]:
                if validation_result["error_code"] == 0:  # 缺少参数
                    # 步骤2: 参数补全
                    completion_result = self.debug_agent.step2_parameter_completion(
                        function_name, validation_result["missing_params"], user_input
                    )

                    if completion_result["clarification_needed"]:
                        return completion_result["question"]
                    else:
                        return "请提供更多信息以完成您的请求。"

                elif validation_result["error_code"] == -1:  # 函数解析错误
                    # 触发超出范围处理
                    out_of_scope_result = self.debug_agent.handle_out_of_scope(user_input)
                    return out_of_scope_result["response"]

            # 步骤3: 执行函数
            function_response = self._execute_function(function_name, function_args)

            # 步骤4: 结果分析
            analysis_result = self.debug_agent.step4_result_analysis(
                function_name, function_args, function_response, user_input
            )

            # 将结果添加到上下文
            self.context[session_id].append({
                "role": "tool",
                "tool_call_id": tool_call.id,
                "name": function_name,
                "content": json.dumps(function_response, ensure_ascii=False)
            })

            # 处理候选项和建议
            if analysis_result["candidates"]:
                return self._format_candidates_response(analysis_result["candidates"], function_name)
            elif analysis_result["need_clarification"]:
                return "结果可能不准确，请确认您的输入信息。"
            else:
                # 生成最终简洁回复
                return self._generate_final_response(session_id, analysis_result["brief_response"])

        return "处理完成。"

    def _execute_function(self, function_name: str, args: dict) -> dict:
        """执行具体的函数调用"""
        try:
            if function_name == "mcp_geocode_address":
                result = mcp_geocode_address(
                    address=args["address"],
                    city=args.get("city")
                )
            elif function_name == "mcp_get_city_code":
                result = mcp_get_city_code(
                    city_name=args["city_name"]
                )
            elif function_name == "mcp_search_poi":
                result = mcp_search_poi(
                    keyword=args["keyword"],
                    city=args.get("city"),
                    types=args.get("types")
                )
            elif function_name == "call_taxi_service":
                raw_result = call_taxi_service(
                    start_place=args["start_place"],
                    end_place=args["end_place"],
                    car_prefer=args.get("car_prefer", "")
                )
                result = self._format_taxi_service_result(raw_result)
            else:
                return {"status": False, "error": f"未知的工具: {function_name}"}

            return result

        except Exception as e:
            return {"status": False, "error": str(e)}

    def _format_candidates_response(self, candidates: list, function_name: str) -> str:
        """格式化候选项响应"""
        if function_name == "mcp_search_poi":
            if len(candidates) == 1:
                return f"找到：{candidates[0]['name']}"
            else:
                response = f"找到{len(candidates)}个地点："
                for i, candidate in enumerate(candidates, 1):
                    response += f"\n{i}. {candidate['name']}"
                return response
        return "已找到相关信息。"

    def _generate_final_response(self, session_id: str, brief_response: str) -> str:
        """生成最终的简洁回复"""
        try:
            # 获取最终回复
            final_response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id] + [
                    {"role": "system", "content": "请用最简洁的语言回复，不超过20字，适合语音输出。"}
                ]
            )

            final_message = final_response.choices[0].message.content
            final_message = self._optimize_response_brevity(final_message)

            self.context[session_id].append({
                "role": "assistant",
                "content": final_message
            })

            return final_message

        except Exception as e:
            return brief_response or "处理完成。"

    def _optimize_response_brevity(self, response: str) -> str:
        """优化回复简洁性"""
        if not response:
            return "好的。"

        # 移除冗余的礼貌用语
        response = response.replace("非常感谢您的", "").replace("很高兴为您", "")
        response = response.replace("希望这些信息对您有帮助", "").replace("如果您还有其他问题", "")

        # 限制长度
        if len(response) > 50:
            # 尝试提取关键信息
            sentences = response.split('。')
            if sentences:
                response = sentences[0] + "。"

        return response.strip()

    def _is_out_of_scope(self, user_input: str) -> bool:
        """判断是否超出服务范围"""
        out_of_scope_keywords = [
            "天气", "新闻", "股票", "电影", "音乐", "游戏",
            "购物", "做饭", "健康", "医疗", "法律", "翻译"
        ]

        user_input_lower = user_input.lower()
        return any(keyword in user_input_lower for keyword in out_of_scope_keywords)

    def _format_taxi_service_result(self, raw_result) -> dict:
        """格式化打车服务结果"""
        if isinstance(raw_result, dict) and raw_result.get("status"):
            return {
                "status": True,
                "data": {
                    "message": "打车服务已安排",
                    "estimated_price": raw_result.get("data", {}).get("estimated_price", "未知"),
                    "distance_km": raw_result.get("data", {}).get("distance_km", "未知")
                }
            }
        else:
            return {"status": False, "error": "打车服务暂不可用"}


# 保持向后兼容的别名
EnhancedTaxiAgent = EnhancedLifeAssistant


def test_enhanced_life_assistant():
    """测试增强版生活助手"""
    print("=== 测试智能生活助手 ===")

    assistant = EnhancedLifeAssistant()

    # 测试用例
    test_cases = [
        "你好",
        "西湖在哪里？",
        "帮我查一下杭州的城市代码",
        "北京有哪些星巴克？",
        "我要从康德大厦打车到太阳宫",
        "今天天气怎么样？",  # 超出范围的请求
        "从北京站到首都机场"  # 缺少参数的请求
    ]

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        try:
            response = assistant.process_message(test_input, f"test_session_{i}")
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 50)


if __name__ == "__main__":
    test_enhanced_life_assistant()
