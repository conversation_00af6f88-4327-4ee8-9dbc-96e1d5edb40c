"""
高德地图MCP工具集
提供地点到经纬度和地点到城市ID的转换功能
"""

import requests
import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class AmapMCPTools:
    """高德地图MCP工具类"""
    
    def __init__(self, api_key: str = None):
        """
        初始化高德地图工具
        
        Args:
            api_key: 高德地图API密钥，如果不提供则从环境变量AMAP_API_KEY获取
        """
        self.api_key = api_key or os.getenv("AMAP_API_KEY")
        if not self.api_key:
            raise ValueError("请提供高德地图API密钥，可通过参数或环境变量AMAP_API_KEY设置")
        
        # 高德地图API基础URL
        self.base_url = "https://restapi.amap.com/v3"
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def geocode_address(self, address: str, city: str = None) -> Dict:
        """
        地点名称转换为经纬度坐标（地理编码）
        
        Args:
            address: 地点名称或地址
            city: 城市名称，可选，用于提高搜索精度
            
        Returns:
            Dict: 包含经纬度信息的字典
            {
                "status": bool,
                "data": {
                    "longitude": float,
                    "latitude": float,
                    "formatted_address": str,
                    "province": str,
                    "city": str,
                    "district": str,
                    "adcode": str
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'address': address,
                'output': 'json'
            }
            
            if city:
                params['city'] = city
            
            # 发送请求
            url = f"{self.base_url}/geocode/geo"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            geocodes = result.get('geocodes', [])
            if not geocodes:
                return {
                    "status": False,
                    "error": f"未找到地点: {address}"
                }
            
            # 取第一个结果
            geocode = geocodes[0]
            location = geocode.get('location', '').split(',')
            
            if len(location) != 2:
                return {
                    "status": False,
                    "error": "坐标格式错误"
                }
            
            return {
                "status": True,
                "data": {
                    "longitude": float(location[0]),
                    "latitude": float(location[1]),
                    "formatted_address": geocode.get('formatted_address', ''),
                    "province": geocode.get('province', ''),
                    "city": geocode.get('city', ''),
                    "district": geocode.get('district', ''),
                    "adcode": geocode.get('adcode', '')
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def get_city_code(self, city_name: str) -> Dict:
        """
        获取城市的adcode（城市ID）
        
        Args:
            city_name: 城市名称
            
        Returns:
            Dict: 包含城市ID信息的字典
            {
                "status": bool,
                "data": {
                    "city_code": str,
                    "adcode": str,
                    "city_name": str,
                    "province": str,
                    "center": {
                        "longitude": float,
                        "latitude": float
                    }
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'keywords': city_name,
                'subdistrict': '0',  # 不返回下级行政区
                'output': 'json'
            }
            
            # 发送请求
            url = f"{self.base_url}/config/district"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            districts = result.get('districts', [])
            if not districts:
                return {
                    "status": False,
                    "error": f"未找到城市: {city_name}"
                }
            
            # 取第一个结果
            district = districts[0]
            center = district.get('center', '').split(',')
            
            center_coords = {}
            if len(center) == 2:
                center_coords = {
                    "longitude": float(center[0]),
                    "latitude": float(center[1])
                }
            
            return {
                "status": True,
                "data": {
                    "city_code": district.get('citycode', ''),
                    "adcode": district.get('adcode', ''),
                    "city_name": district.get('name', ''),
                    "province": district.get('name', ''),
                    "center": center_coords
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def batch_geocode(self, addresses: List[str], city: str = None) -> Dict:
        """
        批量地理编码
        
        Args:
            addresses: 地点名称列表
            city: 城市名称，可选
            
        Returns:
            Dict: 批量处理结果
        """
        results = []
        for address in addresses:
            result = self.geocode_address(address, city)
            results.append({
                "address": address,
                "result": result
            })
        
        return {
            "status": True,
            "data": {
                "total": len(addresses),
                "results": results
            }
        }
    
    def search_poi(self, keyword: str, city: str = None, types: str = None) -> Dict:
        """
        POI搜索（兴趣点搜索）
        
        Args:
            keyword: 搜索关键词
            city: 城市名称
            types: POI类型，如"餐饮服务|购物服务"
            
        Returns:
            Dict: POI搜索结果
        """
        try:
            params = {
                'key': self.api_key,
                'keywords': keyword,
                'output': 'json',
                'offset': 20,  # 每页记录数
                'page': 1,     # 当前页数
                'extensions': 'all'  # 返回详细信息
            }
            
            if city:
                params['city'] = city
            if types:
                params['types'] = types
            
            url = f"{self.base_url}/place/text"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            pois = result.get('pois', [])
            processed_pois = []
            
            for poi in pois:
                location = poi.get('location', '').split(',')
                poi_data = {
                    "name": poi.get('name', ''),
                    "address": poi.get('address', ''),
                    "type": poi.get('type', ''),
                    "tel": poi.get('tel', ''),
                    "longitude": float(location[0]) if len(location) >= 2 else None,
                    "latitude": float(location[1]) if len(location) >= 2 else None,
                    "adcode": poi.get('adcode', ''),
                    "cityname": poi.get('cityname', '')
                }
                processed_pois.append(poi_data)
            
            return {
                "status": True,
                "data": {
                    "count": result.get('count', 0),
                    "pois": processed_pois
                }
            }
            
        except Exception as e:
            return {
                "status": False,
                "error": f"POI搜索错误: {str(e)}"
            }


# 使用示例和测试函数
def test_amap_tools():
    """测试高德地图工具"""
    # 需要设置环境变量 AMAP_API_KEY
    try:
        amap = AmapMCPTools()
        
        # 测试地理编码
        print("=== 测试地理编码 ===")
        result1 = amap.geocode_address("西湖", "杭州")
        print(f"西湖坐标: {result1}")
        
        # 测试城市代码获取
        print("\n=== 测试城市代码 ===")
        result2 = amap.get_city_code("杭州")
        print(f"杭州城市代码: {result2}")
        
        # 测试POI搜索
        print("\n=== 测试POI搜索 ===")
        result3 = amap.search_poi("星巴克", "杭州")
        print(f"杭州星巴克: {result3}")
        
    except Exception as e:
        print(f"测试失败: {e}")


# MCP工具函数定义，用于集成到现有系统
def mcp_geocode_address(address: str, city: str = None) -> Dict:
    """
    MCP工具：地点转经纬度
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.geocode_address(address, city)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_get_city_code(city_name: str) -> Dict:
    """
    MCP工具：获取城市ID
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.get_city_code(city_name)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_search_poi(keyword: str, city: str = None, types: str = None) -> Dict:
    """
    MCP工具：POI搜索
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.search_poi(keyword, city, types)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


# 高德地图工具的function calling定义
AMAP_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "mcp_geocode_address",
            "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
            "parameters": {
                "type": "object",
                "properties": {
                    "address": {
                        "type": "string",
                        "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                    }
                },
                "required": ["address"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_get_city_code",
            "description": "获取城市的adcode（城市ID）和相关信息。",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {
                        "type": "string",
                        "description": "城市名称，如'杭州'、'北京'、'上海'"
                    }
                },
                "required": ["city_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search_poi",
            "description": "搜索兴趣点（POI），如商店、餐厅、景点等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词，如'星巴克'、'加油站'、'医院'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于限定搜索范围"
                    },
                    "types": {
                        "type": "string",
                        "description": "POI类型，可选，如'餐饮服务'、'购物服务'等"
                    }
                },
                "required": ["keyword"]
            }
        }
    }
]


if __name__ == "__main__":
    test_amap_tools()
