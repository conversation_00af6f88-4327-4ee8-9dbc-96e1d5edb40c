#!/usr/bin/env python3
"""
智能打车助手演示启动器
一键启动完整的演示环境
"""

import os
import sys
import subprocess
import time
import webbrowser
import threading
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🚗 智能打车助手演示系统                      ║
    ║                                                              ║
    ║              基于高德地图API和AI技术的智能打车服务               ║
    ║                                                              ║
    ║  支持多种界面: Streamlit | React | Gradio | CLI | API        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 检查并安装依赖包...")
    
    try:
        # 升级pip
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      capture_output=True, check=True)
        
        # 安装requirements.txt中的包
        if Path('requirements.txt').exists():
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                          check=True)
            print("✅ 依赖包安装完成")
        else:
            # 手动安装核心包
            core_packages = [
                'streamlit>=1.28.0',
                'gradio>=4.0.0', 
                'flask>=2.3.0',
                'flask-cors>=4.0.0',
                'openai>=1.0.0',
                'requests>=2.31.0',
                'pandas>=2.0.0',
                'plotly>=5.15.0'
            ]
            
            for package in core_packages:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                              check=True)
            print("✅ 核心依赖包安装完成")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def check_environment_variables():
    """检查环境变量"""
    print("🔍 检查环境变量...")
    
    required_vars = {
        'BAILIAN_API_KEY': '百炼AI API密钥',
        'AMAP_API_KEY': '高德地图API密钥'
    }
    
    missing_vars = []
    
    for var, desc in required_vars.items():
        if os.getenv(var):
            print(f"✅ {desc}: 已设置")
        else:
            print(f"⚠️  {desc}: 未设置")
            missing_vars.append(var)
    
    if missing_vars:
        print("\n📝 环境变量设置说明:")
        print("请在终端中运行以下命令设置环境变量:")
        for var in missing_vars:
            print(f"export {var}='your_{var.lower()}'")
        
        print("\n或者创建 .env 文件:")
        for var in missing_vars:
            print(f"{var}=your_{var.lower()}")
        
        return False
    
    return True

def start_api_server_background():
    """在后台启动API服务器"""
    print("🚀 启动API服务器...")
    
    try:
        # 启动API服务器
        process = subprocess.Popen(
            [sys.executable, 'api_server.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否正常运行
        import requests
        try:
            response = requests.get('http://localhost:8000/api/health', timeout=5)
            if response.status_code == 200:
                print("✅ API服务器启动成功")
                return process
            else:
                print("❌ API服务器启动失败")
                return None
        except:
            print("❌ API服务器无法访问")
            return None
            
    except Exception as e:
        print(f"❌ 启动API服务器失败: {e}")
        return None

def start_streamlit_interface():
    """启动Streamlit界面"""
    print("🌐 启动Streamlit界面...")
    
    try:
        # 启动Streamlit
        process = subprocess.Popen([
            sys.executable, '-m', 'streamlit', 'run', 
            'taxi_demo_app.py',
            '--server.port', '8501',
            '--server.address', '0.0.0.0',
            '--server.headless', 'true'
        ])
        
        # 等待启动
        time.sleep(5)
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:8501')
        print("✅ Streamlit界面已启动: http://localhost:8501")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动Streamlit失败: {e}")
        return None

def start_gradio_interface():
    """启动Gradio界面"""
    print("🎨 启动Gradio界面...")
    
    try:
        process = subprocess.Popen([sys.executable, 'gradio_demo.py'])
        
        # 等待启动
        time.sleep(5)
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:7860')
        print("✅ Gradio界面已启动: http://localhost:7860")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动Gradio失败: {e}")
        return None

def show_access_info():
    """显示访问信息"""
    print("\n" + "="*60)
    print("🌐 演示系统访问信息")
    print("="*60)
    print("📱 Streamlit界面:  http://localhost:8501")
    print("🎨 Gradio界面:     http://localhost:7860") 
    print("🔧 API服务器:      http://localhost:8000")
    print("📚 API文档:        http://localhost:8000/api/health")
    print("-"*60)
    print("💡 提示:")
    print("- 推荐使用Streamlit界面，功能最完整")
    print("- Gradio界面更简洁，适合快速测试")
    print("- 按 Ctrl+C 停止所有服务")
    print("="*60)

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    print("\n📦 准备环境...")
    if not install_dependencies():
        print("❌ 环境准备失败")
        return 1
    
    # 检查环境变量
    env_ok = check_environment_variables()
    if not env_ok:
        print("\n⚠️  环境变量不完整，某些功能可能不可用")
        choice = input("是否继续启动演示? (y/N): ").strip().lower()
        if choice != 'y':
            return 1
    
    print("\n🚀 启动演示系统...")
    
    processes = []
    
    try:
        # 启动API服务器
        api_process = start_api_server_background()
        if api_process:
            processes.append(api_process)
        
        # 启动Streamlit界面
        streamlit_process = start_streamlit_interface()
        if streamlit_process:
            processes.append(streamlit_process)
        
        # 启动Gradio界面
        gradio_process = start_gradio_interface()
        if gradio_process:
            processes.append(gradio_process)
        
        if not processes:
            print("❌ 所有服务启动失败")
            return 1
        
        # 显示访问信息
        show_access_info()
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 正在停止所有服务...")
            
            # 停止所有进程
            for process in processes:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    process.kill()
            
            print("✅ 所有服务已停止")
            return 0
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        
        # 清理进程
        for process in processes:
            try:
                process.terminate()
            except:
                pass
        
        return 1

if __name__ == '__main__':
    sys.exit(main())
