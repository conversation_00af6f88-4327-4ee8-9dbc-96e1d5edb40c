import unittest
import requests
import json
from mock_taxi_service import app

class TestTaxiService(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        app.testing = True
        cls.client = app.test_client()
        cls.base_url = 'http://localhost:5000'

    def test_search_city_code_by_coordinate(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1913228968434073600\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"106,34\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"search_city_code_by_coordinate",
            "scriptParams":"{\"longitude\":\"120.54321\",\"latitude\":\"30.54321\"}",
            "debug": "false"
        }
        response = self.client.post('/search_city_code_by_coordinate', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        self.assertEqual(result['code'], 0)
        self.assertEqual(result['message'], 'success')
        self.assertIn('city_code', json.loads(result['data']))

    def test_search_spot_by_coordinate(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1913228968434073600\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"106,34\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"search_spot_by_coordinate",
            "scriptParams":"{\"longitude\":\"120.54321\",\"latitude\":\"30.54321\"}",
            "debug": "false"
        }
        response = self.client.post('/search_spot_by_coordinate', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        self.assertEqual(result['code'], 0)
        data = json.loads(result['data'])
        self.assertIsInstance(data['list'], list)
        self.assertGreater(len(data['list']), 0)

    def test_order_car(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1900437112298143744\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"116.306942,40.040858\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName": "order_car",
            "scriptParams": "{\"from_longitude\":\"116.306942\",\"from_latitude\":\"40.040858\",\"to_longitude\":\"116.340941\",\"to_latitude\":\"40.052857\",\"car_type\":\"3\",\"order_type\":\"1\",\"city_code\":\"44\",\"caller_phone\":\"13100010001\",\"estimate_price\":\"1230\",\"estimate_price_key\":\"xjfag\",\"start_name\":\"a1\",\"start_address\":\"a2\",\"end_name\":\"b1\",\"end_address\":\"b2\"}",
            "debug": "false"
        }
        response = self.client.post('/order_car', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        data = json.loads(result['data'])
        self.assertIn('order_id', data)
        self.assertIn('external_order_id', data)

    def test_get_estimate_price(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1913228968434073600\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"106,34\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"get_estimate_price",
            "scriptParams":"{\"from_longitude\":\"30.54321\",\"from_latitude\":\"111\",\"to_longitude\":\"30.54321\",\"to_latitude\":\"112\",\"car_type\":\"3\",\"order_type\":\"1\",\"city_code\":\"123\"}",
            "debug": "false"
        }
        response = self.client.post('/get_estimate_price', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        data = json.loads(result['data'])
        self.assertIsInstance(data['list'], list)
        self.assertGreater(len(data['list']), 0)

    def test_cancel_car(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1920295277764280300\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"116.306942,40.040858\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"cancel_car",
            "scriptParams":"{\"order_id\":\"************\",\"external_order_id\":\"10001\",\"cancel_code\":\"1\",\"cancel_reason\":\"取消测试订单,行程有变化\",\"who_cancel\":\"1\"}",
            "debug": "false"
        }
        response = self.client.post('/cancel_car', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        data = json.loads(result['data'])
        self.assertIn('order_no', data)

    def test_history_order(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1913228968434073600\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"106,34\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"history_order",
            "scriptParams":"",
            "debug": "false"
        }
        response = self.client.post('/history_order', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        data = json.loads(result['data'])
        self.assertIsInstance(data['list'], list)

    def test_find_last_order(self):
        data = {
            "context": "{\"req_id\": \"20001\", \"uid\": \"1913228968434073600\", \"intent_id\": \"40001\", \"conversation_id\": \"30001\", \"coords\": \"106,34\", \"app_id\": \"com.caocao\"}",
            "scriptId": "1000009",
            "subInteractiveName":"find_last_order",
            "scriptParams":"{\"user_id\":\"1913228968434073600\",\"channel\":\"caocao\",\"parameter\":\"{}\"}",
            "debug": "false"
        }
        response = self.client.post('/find_last_order', json=data)
        self.assertEqual(response.status_code, 200)
        result = json.loads(response.data)
        self.assertTrue(result['status'])
        data = json.loads(result['data'])
        self.assertIn('order', data)

if __name__ == '__main__':
    unittest.main()
