import requests
import json
import time
import uuid
url = "http://************:7002/v1/chat-messages"
headers = {
    "Authorization": "Bearer app-7CmMZKFzUF7to6aGY2MmQNJG",
    # "Authorization":"Bearer app-ispuylkyCGzT96o0dt5ONI1v",
    "Content-Type": "application/json"
}

history_str = ""

while True:
    user_input = input("\n 你：")
    if user_input.strip().lower() in ["exit", "quit"]:
        print("会话结束。")
        break

    payload = {
        "inputs": {
            "history_str": history_str,
            "user_id": "123456",
            "input": user_input,
        },
        "response_mode": "streaming",
        "query": "yechaoying—-test",
        "conversation_id": "",
        "user_id": "************",
        "user": "************"
    }

    start_time = time.time()
    response = requests.post(url, json=payload, headers=headers, stream=True)

    first_byte_time = None
    full_answer = ""

    for line in response.iter_lines(decode_unicode=True):
        if line:
            # print(line)
            if line.startswith("data: "):
                try:
                    data_str = line[len("data: "):]
                    data_json = json.loads(data_str)
                    if "answer" in data_json:
                        if first_byte_time is None:
                            first_byte_time = time.time()
                            elapsed_ms = int((first_byte_time - start_time) * 1000)
                            print(f"\n🕒 首字节耗时: {elapsed_ms} ms\n")
                        print(data_json["answer"], end="", flush=True)
                        full_answer += data_json["answer"]
                except json.JSONDecodeError:
                    print(f"\n[解析错误]: {line}")
    # print("\n完整回答：", full_answer)

    # 可选：更新历史对话字符串
    print("\n------history_str-------")
    history_str += f"\nuser: {user_input}\nbot: {full_answer}"
    print( history_str)
    print("-------------")