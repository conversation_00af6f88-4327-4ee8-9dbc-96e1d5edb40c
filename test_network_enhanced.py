#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联网搜索和智能判断功能测试
测试大模型联网搜索、模糊地点处理、智能建议等功能
"""

import json
import uuid
import time
from taxi_agent_system import EnhancedTaxiAgent


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def test_network_search_capabilities():
    """测试联网搜索能力"""
    print("=== 联网搜索能力测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试超出范围的请求，使用联网搜索
    network_test_cases = [
        "帮我查下三星集团2024年的盈利情况",
        "今天北京的天气怎么样？",
        "推荐一些最新的科幻电影",
        "苹果公司最新的产品发布会信息"
    ]
    
    for i, request in enumerate(network_test_cases, 1):
        print(f"\n--- 联网搜索测试 {i} ---")
        print(f"用户请求: {request}")
        
        try:
            # 分析请求范围
            analysis = agent._analyze_out_of_scope_request(request)
            print(f"范围分析: {analysis}")
            
            # 提供智能建议
            suggestions = agent._provide_intelligent_suggestions(request)
            print(f"智能建议: {suggestions}")
            
            # 完整对话测试
            start_time = time.time()
            response = agent.process_message(request, session_id)
            end_time = time.time()
            
            print(f"系统回复: {response[:200]}...")  # 截取前200字符
            print(f"响应时间: {end_time - start_time:.2f}s")
            
        except Exception as e:
            print(f"测试失败: {e}")
        
        print("-" * 60)
        time.sleep(2)  # 避免请求过快


def test_ambiguous_location_handling():
    """测试模糊地点处理"""
    print("\n=== 模糊地点处理测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试各种模糊地点
    ambiguous_locations = [
        {"location": "首都机场", "city": "北京"},
        {"location": "大悦城", "city": "北京"},
        {"location": "万达广场", "city": "上海"},
        {"location": "火车站", "city": "杭州"},
        {"location": "人民医院", "city": "深圳"}
    ]
    
    for test_case in ambiguous_locations:
        location = test_case["location"]
        city = test_case["city"]
        
        print(f"\n--- 处理模糊地点: {location} ({city}) ---")
        
        try:
            result = agent._handle_ambiguous_location(location, city)
            
            print(f"是否模糊: {result.get('is_ambiguous', False)}")
            print(f"处理状态: {result.get('status', False)}")
            print(f"消息: {result.get('message', '')}")
            
            if result.get("recommendations"):
                print("推荐地点:")
                for rec in result["recommendations"][:3]:  # 显示前3个
                    print(f"  {rec['index']}. {rec['name']} - {rec['address']}")
            
        except Exception as e:
            print(f"处理失败: {e}")
        
        print("-" * 50)


def test_llm_location_validity():
    """测试大模型地点有效性判断"""
    print("\n=== 大模型地点有效性判断测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试各种地点组合
    location_test_cases = [
        {"start": "火星", "end": "金星", "expected": "超出服务范围"},
        {"start": "康德大厦", "end": "康德大厦", "expected": "起点终点相同"},
        {"start": "机场", "end": "大悦城", "expected": "地点模糊"},
        {"start": "北京", "end": "上海", "expected": "距离过远"},
        {"start": "康德大厦", "end": "太阳宫", "expected": "正常路线"}
    ]
    
    for test_case in location_test_cases:
        start_place = test_case["start"]
        end_place = test_case["end"]
        expected = test_case["expected"]
        
        print(f"\n--- 地点有效性: {start_place} → {end_place} ---")
        print(f"预期结果: {expected}")
        
        try:
            validity_result = agent._llm_analyze_location_validity(start_place, end_place)
            print(f"大模型分析: {validity_result}")
            
            # 检查是否符合预期
            if any(keyword in validity_result for keyword in expected.split()):
                print("✅ 分析结果符合预期")
            else:
                print("⚠️  分析结果与预期不完全匹配")
                
        except Exception as e:
            print(f"分析失败: {e}")
        
        print("-" * 50)


def test_intelligent_taxi_booking():
    """测试智能打车预订流程"""
    print("\n=== 智能打车预订流程测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟包含模糊地点的打车请求
    taxi_scenarios = [
        "我要从首都机场打车到大悦城",
        "从火车站到万达广场，要舒适型车辆",
        "我在医院，想去机场",
        "从康德大厦到太阳宫地铁站"
    ]
    
    for i, scenario in enumerate(taxi_scenarios, 1):
        print(f"\n--- 智能打车场景 {i} ---")
        print(f"用户请求: {scenario}")
        
        try:
            start_time = time.time()
            response = agent.process_message(scenario, session_id)
            end_time = time.time()
            
            print(f"系统回复: {response}")
            print(f"响应时间: {end_time - start_time:.2f}s")
            
            # 检查是否提供了地点选择建议
            if "选择" in response or "推荐" in response or "具体" in response:
                print("✅ 系统智能识别了模糊地点")
            else:
                print("ℹ️  系统直接处理了请求")
                
        except Exception as e:
            print(f"处理失败: {e}")
        
        print("-" * 50)
        time.sleep(1)


def test_mcp_tool_suggestions():
    """测试MCP工具建议功能"""
    print("\n=== MCP工具建议功能测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试可能需要MCP工具的请求
    mcp_scenarios = [
        "帮我查询股票价格",
        "我想知道今天的新闻",
        "帮我翻译一段英文",
        "查询天气预报",
        "帮我发送邮件"
    ]
    
    for scenario in mcp_scenarios:
        print(f"\n--- MCP工具建议: {scenario} ---")
        
        try:
            # 分析请求
            analysis = agent._analyze_out_of_scope_request(scenario)
            print(f"请求分析: {analysis}")
            
            # 获取智能建议
            suggestions = agent._provide_intelligent_suggestions(scenario)
            print(f"建议方案: {suggestions}")
            
            # 检查是否提到了MCP或API配置
            if "MCP" in suggestions or "API" in suggestions or "配置" in suggestions:
                print("✅ 提供了MCP/API配置建议")
            else:
                print("ℹ️  提供了其他解决方案")
                
        except Exception as e:
            print(f"分析失败: {e}")
        
        print("-" * 40)


def test_error_recovery_with_suggestions():
    """测试带建议的错误恢复"""
    print("\n=== 带建议的错误恢复测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟各种错误场景
    error_scenarios = [
        {"input": "我要从火星打车到金星", "type": "不合理参数"},
        {"input": "帮我叫车", "type": "参数缺失"},
        {"input": "从机场到大悦城", "type": "模糊地点"},
        {"input": "预订酒店房间", "type": "功能超范围"}
    ]
    
    for scenario in error_scenarios:
        user_input = scenario["input"]
        error_type = scenario["type"]
        
        print(f"\n--- 错误恢复: {error_type} ---")
        print(f"用户输入: {user_input}")
        
        try:
            response = agent.process_message(user_input, session_id)
            print(f"系统回复: {response}")
            
            # 检查是否提供了有用的建议
            suggestion_keywords = ["建议", "推荐", "可以", "尝试", "使用"]
            if any(keyword in response for keyword in suggestion_keywords):
                print("✅ 提供了有用的建议")
            else:
                print("⚠️  缺少具体建议")
                
        except Exception as e:
            print(f"处理失败: {e}")
        
        print("-" * 50)


def main():
    """主测试函数"""
    print("联网搜索和智能判断功能测试")
    print("=" * 60)
    
    # 1. 测试联网搜索能力
    test_network_search_capabilities()
    
    # 2. 测试模糊地点处理
    test_ambiguous_location_handling()
    
    # 3. 测试大模型地点有效性判断
    test_llm_location_validity()
    
    # 4. 测试智能打车预订流程
    test_intelligent_taxi_booking()
    
    # 5. 测试MCP工具建议
    test_mcp_tool_suggestions()
    
    # 6. 测试错误恢复与建议
    test_error_recovery_with_suggestions()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    print("🎯 新增功能验证:")
    print("✅ 联网搜索能力 - 使用enable_search获取最新信息")
    print("✅ 模糊地点处理 - 自动POI搜索和推荐")
    print("✅ 大模型智能判断 - 地点有效性分析")
    print("✅ MCP工具建议 - 引导用户配置相关工具")
    print("✅ 错误恢复增强 - 提供具体可行的建议")
    
    print(f"\n💡 核心改进:")
    print("- 超出范围请求通过联网搜索提供准确信息")
    print("- 模糊地点自动解析为具体位置选项")
    print("- 大模型智能判断参数合理性")
    print("- 引导用户配置MCP工具扩展功能")
    print("- 提供可操作的错误恢复建议")


if __name__ == "__main__":
    main()
