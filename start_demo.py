#!/usr/bin/env python3
"""
智能打车助手演示启动脚本
支持多种前端界面：Streamlit、React、命令行
"""

import os
import sys
import subprocess
import time
import argparse
import webbrowser
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'streamlit',
        'flask',
        'flask-cors',
        'requests',
        'openai'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_environment():
    """检查环境变量"""
    required_env = ['BAILIAN_API_KEY', 'AMAP_API_KEY']
    missing_env = []
    
    for env_var in required_env:
        if not os.getenv(env_var):
            missing_env.append(env_var)
    
    if missing_env:
        print("⚠️  缺少以下环境变量:")
        for env_var in missing_env:
            print(f"   - {env_var}")
        print("\n请设置这些环境变量以获得完整功能")
        return False
    
    print("✅ 环境变量配置完整")
    return True

def start_streamlit_demo():
    """启动Streamlit演示"""
    print("🚀 启动Streamlit演示...")
    
    if not Path('taxi_demo_app.py').exists():
        print("❌ 找不到 taxi_demo_app.py 文件")
        return False
    
    try:
        # 启动Streamlit应用
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 
            'taxi_demo_app.py',
            '--server.port', '8501',
            '--server.address', '0.0.0.0'
        ])
        return True
    except KeyboardInterrupt:
        print("\n👋 Streamlit演示已停止")
        return True
    except Exception as e:
        print(f"❌ 启动Streamlit失败: {e}")
        return False

def start_api_server():
    """启动API服务器"""
    print("🚀 启动API服务器...")
    
    if not Path('api_server.py').exists():
        print("❌ 找不到 api_server.py 文件")
        return False
    
    try:
        subprocess.run([sys.executable, 'api_server.py'])
        return True
    except KeyboardInterrupt:
        print("\n👋 API服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动API服务器失败: {e}")
        return False

def start_react_demo():
    """启动React演示"""
    print("🚀 启动React演示...")
    
    react_dir = Path('react_chat_demo')
    if not react_dir.exists():
        print("❌ 找不到 react_chat_demo 目录")
        return False
    
    # 检查是否安装了Node.js
    try:
        subprocess.run(['node', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 需要安装 Node.js 才能运行React演示")
        print("请访问 https://nodejs.org/ 下载安装")
        return False
    
    # 检查是否安装了依赖
    if not (react_dir / 'node_modules').exists():
        print("📦 安装React依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=react_dir, check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装依赖失败: {e}")
            return False
    
    try:
        # 启动React开发服务器
        subprocess.run(['npm', 'start'], cwd=react_dir)
        return True
    except KeyboardInterrupt:
        print("\n👋 React演示已停止")
        return True
    except Exception as e:
        print(f"❌ 启动React演示失败: {e}")
        return False

def start_cli_demo():
    """启动命令行演示"""
    print("🚀 启动命令行演示...")
    
    try:
        from taxi_agent_system import EnhancedTaxiAgent
        
        agent = EnhancedTaxiAgent()
        session_id = "cli_demo"
        
        print("\n" + "="*50)
        print("🚗 智能打车助手 - 命令行版本")
        print("="*50)
        print("支持功能:")
        print("1. 🗺️  地点查询和坐标转换")
        print("2. 🏙️  城市信息和代码查询") 
        print("3. 📍 POI搜索和商户查找")
        print("4. 🚗 智能打车和价格估算")
        print("\n输入 'quit' 或 'exit' 退出")
        print("输入 'help' 查看示例")
        print("-"*50)
        
        while True:
            try:
                user_input = input("\n用户: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！")
                    break
                
                if user_input.lower() == 'help':
                    print("\n💡 示例查询:")
                    examples = [
                        "西湖在哪里？",
                        "帮我查一下杭州的城市代码",
                        "北京有哪些星巴克？",
                        "我要从康德大厦打车到太阳宫",
                        "从北京站到首都机场，要舒适型车辆"
                    ]
                    for i, example in enumerate(examples, 1):
                        print(f"{i}. {example}")
                    continue
                
                if not user_input:
                    continue
                
                print("🤖 正在处理...")
                response = agent.process_message(user_input, session_id)
                print(f"助手: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 处理请求时出错: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入打车系统: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动命令行演示失败: {e}")
        return False

def start_gradio_demo():
    """启动Gradio演示"""
    print("🚀 启动Gradio演示...")

    if not Path('gradio_demo.py').exists():
        print("❌ 找不到 gradio_demo.py 文件")
        return False

    # 检查是否安装了Gradio
    try:
        import gradio
    except ImportError:
        print("❌ 需要安装 Gradio 才能运行此演示")
        print("请运行: pip install gradio")
        return False

    try:
        subprocess.run([sys.executable, 'gradio_demo.py'])
        return True
    except KeyboardInterrupt:
        print("\n👋 Gradio演示已停止")
        return True
    except Exception as e:
        print(f"❌ 启动Gradio演示失败: {e}")
        return False

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚗 智能打车助手演示系统")
    print("="*60)
    print("请选择演示模式:")
    print("1. 🌐 Streamlit Web界面 (推荐)")
    print("2. ⚛️  React现代化界面")
    print("3. 🎨 Gradio简洁界面")
    print("4. 🖥️  命令行界面")
    print("5. 🔧 仅启动API服务器")
    print("6. ❌ 退出")
    print("-"*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能打车助手演示启动器')
    parser.add_argument('--mode', choices=['streamlit', 'react', 'gradio', 'cli', 'api'],
                       help='直接指定启动模式')
    parser.add_argument('--skip-checks', action='store_true', 
                       help='跳过依赖和环境检查')
    
    args = parser.parse_args()
    
    print("🚗 智能打车助手演示系统")
    print("基于高德地图API和AI技术")
    print("-"*40)
    
    # 检查依赖和环境
    if not args.skip_checks:
        print("🔍 检查系统环境...")
        deps_ok = check_dependencies()
        env_ok = check_environment()
        
        if not deps_ok:
            return 1
        
        if not env_ok:
            print("⚠️  环境变量不完整，某些功能可能不可用")
            input("按回车键继续...")
    
    # 直接启动指定模式
    if args.mode:
        if args.mode == 'streamlit':
            return 0 if start_streamlit_demo() else 1
        elif args.mode == 'react':
            return 0 if start_react_demo() else 1
        elif args.mode == 'gradio':
            return 0 if start_gradio_demo() else 1
        elif args.mode == 'cli':
            return 0 if start_cli_demo() else 1
        elif args.mode == 'api':
            return 0 if start_api_server() else 1
    
    # 交互式菜单
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (1-5): ").strip()
            
            if choice == '1':
                start_streamlit_demo()
            elif choice == '2':
                start_react_demo()
            elif choice == '3':
                start_gradio_demo()
            elif choice == '4':
                start_cli_demo()
            elif choice == '5':
                start_api_server()
            elif choice == '6':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-6")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出现错误: {e}")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
