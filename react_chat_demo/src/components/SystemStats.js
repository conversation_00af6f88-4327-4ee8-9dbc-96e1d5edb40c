import React from 'react';
import styled from 'styled-components';
import { BarChart3, TrendingUp, Clock, CheckCircle, XCircle } from 'lucide-react';

const StatsContainer = styled.div`
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const StatsTitle = styled.h3`
  color: #333;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const StatItem = styled.div`
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

const StatValue = styled.div`
  font-size: 20px;
  font-weight: 700;
  color: ${props => props.color || '#333'};
  margin-bottom: 4px;
`;

const StatLabel = styled.div`
  font-size: 12px;
  color: #666;
  font-weight: 500;
`;

const SuccessRate = styled.div`
  margin-top: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
`;

const SuccessRateBar = styled.div`
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-top: 8px;
`;

const SuccessRateFill = styled.div`
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  width: ${props => props.percentage}%;
  transition: width 0.3s ease;
`;

const SessionInfo = styled.div`
  margin-top: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 12px;
  color: #666;
`;

const formatDuration = (startTime) => {
  const now = new Date();
  const diff = now - startTime;
  const minutes = Math.floor(diff / 60000);
  const seconds = Math.floor((diff % 60000) / 1000);
  
  if (minutes > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${seconds}秒`;
};

const SystemStats = ({ stats }) => {
  const { totalMessages, successfulRequests, failedRequests, startTime } = stats;
  
  const successRate = totalMessages > 0 ? 
    Math.round((successfulRequests / totalMessages) * 100) : 0;
  
  const duration = formatDuration(startTime);

  return (
    <StatsContainer>
      <StatsTitle>
        <BarChart3 size={16} />
        会话统计
      </StatsTitle>
      
      <StatsGrid>
        <StatItem>
          <StatValue color="#667eea">{totalMessages}</StatValue>
          <StatLabel>总消息数</StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue color="#10b981">{successfulRequests}</StatValue>
          <StatLabel>成功请求</StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue color="#ef4444">{failedRequests}</StatValue>
          <StatLabel>失败请求</StatLabel>
        </StatItem>
        
        <StatItem>
          <StatValue color="#f59e0b">{duration}</StatValue>
          <StatLabel>会话时长</StatLabel>
        </StatItem>
      </StatsGrid>
      
      <SuccessRate>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '4px'
        }}>
          <span style={{ fontSize: '14px', fontWeight: '600', color: '#333' }}>
            成功率
          </span>
          <span style={{ 
            fontSize: '14px', 
            fontWeight: '700', 
            color: successRate >= 80 ? '#10b981' : successRate >= 60 ? '#f59e0b' : '#ef4444'
          }}>
            {successRate}%
          </span>
        </div>
        <SuccessRateBar>
          <SuccessRateFill percentage={successRate} />
        </SuccessRateBar>
      </SuccessRate>
      
      <SessionInfo>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px', marginBottom: '4px' }}>
          <Clock size={12} />
          <span>会话开始时间: {startTime.toLocaleTimeString('zh-CN')}</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
          <TrendingUp size={12} />
          <span>
            状态: {totalMessages === 0 ? '等待中' : 
                   successRate >= 80 ? '良好' : 
                   successRate >= 60 ? '一般' : '需要改进'}
          </span>
        </div>
      </SessionInfo>
    </StatsContainer>
  );
};

export default SystemStats;
