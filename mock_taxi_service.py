from flask import Flask, request, jsonify
from datetime import datetime
import random

app = Flask(__name__)

# 1. 基于坐标查找城市代码
@app.route('/search_city_code_by_coordinate', methods=['POST'])
def search_city_code_by_coordinate():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"city_code\":\"010\",\"city_name\":\"北京市\"}"
    })

# 2. 基于坐标查找上车点
@app.route('/search_spot_by_coordinate', methods=['POST'])
def search_spot_by_coordinate():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"list\":[{\"distance\":197,\"name\":\"浙江省杭州市吉利科技集团(西门)\",\"location\":\"120.12345,30.12345\",\"city_code\":\"123\"},{\"distance\":69,\"name\":\"浙江省杭州市吉利科技集团(南门)\",\"location\":\"120.54321,30.54321\",\"city_code\":\"123\"}]}"
    })

# 3. 基于文本查找上车点
@app.route('/search_spot_by_address', methods=['POST'])
def search_spot_by_address():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"list\":[{\"distance\":197,\"name\":\"浙江省杭州市吉利科技集团(西门)\",\"location\":\"120.12345,30.12345\",\"city_code\":\"123\"},{\"distance\":69,\"name\":\"浙江省杭州市吉利科技集团(南门)\",\"location\":\"120.54321,30.54321\",\"city_code\":\"123\"}]}"
    })

# 4. 获取行程预估价格
@app.route('/get_estimate_price', methods=['POST'])
def get_estimate_price():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"list\":[{\"car_type\":2,\"distance\":12759,\"duration\":1200,\"name\":\"新能源\",\"price\":2250,\"price_key\":\"64f6681a-7a77-4ebd-8e36-e7175612559f\"},{\"car_type\":3,\"distance\":12759,\"duration\":1200,\"name\":\"优选\",\"origin_price\":1460,\"price\":1456,\"price_key\":\"6e56bdb4-3bb0-4482-b3fb-bc2fc78fa428\"}]}"
    })

# 5. 叫车下单
@app.route('/order_car', methods=['POST'])
def order_car():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"caller_phone\":\"13100010001\",\"departure_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"driver_info_vo\":{\"car_brand\":\"雪佛兰\",\"car_type\":\"迈锐宝\",\"card\":\"浙RAA3U8\",\"color\":\"红色\",\"name\":\"任师傅\",\"phone\":\"13100010001\"},\"estimate_price\":2667,\"order_id\":" + str(random.randint(100000000000, 999999999999)) + ",\"external_order_id\":" + str(random.randint(100000000000, 999999999999)) + ",\"order_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"passenger_name\":\"13100010001\",\"status\":9}"
    })

# 6. 查询订单车辆状态
@app.route('/get_ordercar_status', methods=['POST'])
def get_ordercar_status():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"direction\":67.0,\"latitude\":40.041027,\"longitude\":116.306949,\"travel_km\":5.59,\"travel_minute\":19,\"remain_distance\":67,\"remain_time\":100,\"remain_lights\":3}"
    })

# 7. 查询订单详情
@app.route('/get_order_detail', methods=['POST'])
def get_order_detail():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"caller_phone\":\"13100010001\",\"departure_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"driver_info_vo\":{\"car_brand\":\"雪佛兰\",\"car_type\":\"迈锐宝\",\"card\":\"浙RAA3U8\",\"color\":\"红色\",\"name\":\"任师傅\",\"phone\":\"13100010001\"},\"estimate_price\":2667,\"order_id\":970762002000,\"external_order_id\":970762002001,\"order_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"passenger_name\":\"13100010001\",\"status\":9}"
    })

# 8. 取消订单
@app.route('/cancel_car', methods=['POST'])
def cancel_car():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"order_no\":980762002000,\"cancel_fee\":0}"
    })

# 9. 两点间距离查询
@app.route('/get_distance', methods=['POST'])
def get_distance():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"distance\":\"2170\"}"
    })

# 10. 查询历史订单
@app.route('/history_order', methods=['POST'])
def history_order():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"list\":[{\"delete_flag\":false,\"created_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"updated_time\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"version\":0,\"id\":1000047,\"user_id\":1925381617279303700,\"order_id\":9959158990849740,\"channel\":\"shouqi\",\"external_order_id\":\"B250522104209931000\",\"status\":0}]}"
    })

# 11. 订单状态回调
@app.route('/order_status_callback', methods=['POST'])
def order_status_callback():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": ""
    })

# 12. 基于文本查找提示列表
@app.route('/search_tips_by_address', methods=['POST'])
def search_tips_by_address():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"tips\":[{\"id\":\"B0H39CSOVO\",\"name\":\"龙湖云璟\",\"district\":\"北京市昌平区\",\"adcode\":\"110114\",\"location\":\"116.312410,40.100822\",\"address\":\"朱辛庄沙河镇七里渠地块(朱辛庄地铁站B2南出口步行460米)\",\"typecode\":\"120302\",\"city\":[]},{\"id\":\"B0KAZUFRZI\",\"name\":\"龙湖云璟5栋\",\"district\":\"北京市昌平区\",\"adcode\":\"110114\",\"location\":\"116.312356,40.099780\",\"address\":\"朱辛庄沙河镇七里渠地块(朱辛庄地铁站B2南出口步行460米)\",\"typecode\":\"190403\",\"city\":[]}],\"status\":\"1\",\"info\":\"OK\",\"infocode\":\"10000\",\"count\":\"7\"}"
    })

# 13. 订单支付
@app.route('/pay', methods=['POST'])
def pay():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"message\":\"success\",\"code\":0}"
    })

# 14. 查询最近订单
@app.route('/find_last_order', methods=['POST'])
def find_last_order():
    data = request.get_json()
    return jsonify({
        "status": True,
        "code": 0,
        "message": "success",
        "data": "{\"order\":{\"channel\":\"caocao\",\"createdTime\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"deleteFlag\":false,\"externalOrderId\":\"966837102000\",\"id\":1000010,\"orderId\":8526455667907530,\"status\":0,\"subStatus\":9,\"updatedTime\":\"" + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\",\"userId\":1933146146251735000,\"version\":0}}"
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
