#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试位置上下文功能
验证默认经纬度转POI和环境信息集成
"""

import os
from taxi_agent_system import EnhancedTaxiAgent, MainAgent, DEFAULT_LONGITUDE, DEFAULT_LATITUDE


def test_location_context_initialization():
    """测试位置上下文初始化"""
    print("🌍 测试位置上下文初始化")
    print("=" * 50)
    
    # 检查环境变量
    print(f"默认经度: {DEFAULT_LONGITUDE}")
    print(f"默认纬度: {DEFAULT_LATITUDE}")
    print(f"位置上下文启用: {os.getenv('LOCATION_CONTEXT_ENABLED', 'true')}")
    
    # 初始化Agent
    print("\n初始化EnhancedTaxiAgent...")
    agent = EnhancedTaxiAgent()
    
    # 检查位置上下文
    location_context = agent.get_location_context()
    print(f"\n📍 位置上下文信息:")
    print(f"默认坐标: ({location_context['default_longitude']}, {location_context['default_latitude']})")
    
    if location_context.get("current_location_info"):
        info = location_context["current_location_info"]
        print(f"当前位置: {info.get('formatted_address', '未知')}")
        
        nearby_pois = info.get("nearby_pois", [])
        if nearby_pois:
            print(f"附近POI ({len(nearby_pois)}个):")
            for i, poi in enumerate(nearby_pois, 1):
                name = poi.get("name", "")
                poi_type = poi.get("type", "")
                distance = poi.get("distance", 0)
                print(f"  {i}. {name} ({poi_type}, {distance}米)")
        else:
            print("附近POI: 无")
    else:
        print("位置信息: 未初始化")
    
    # 检查系统提示词
    print(f"\n📝 系统提示词预览:")
    prompt_preview = agent.system_prompt[:200] + "..." if len(agent.system_prompt) > 200 else agent.system_prompt
    print(prompt_preview)
    
    return agent


def test_location_context_in_conversation():
    """测试位置上下文在对话中的应用"""
    print("\n\n🗣️ 测试位置上下文在对话中的应用")
    print("=" * 50)
    
    agent = EnhancedTaxiAgent()
    
    # 测试用例
    test_cases = [
        "我在哪里？",
        "附近有什么？",
        "我要打车去最近的商场",
        "推荐附近的餐厅",
        "从这里到天安门怎么走？"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        try:
            response = agent.process_message(test_input, f"location_test_{i}")
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 30)


def test_location_update():
    """测试位置更新功能"""
    print("\n\n📍 测试位置更新功能")
    print("=" * 50)
    
    agent = EnhancedTaxiAgent()
    
    # 显示当前位置
    current_context = agent.get_location_context()
    current_info = current_context.get("current_location_info")
    if current_info:
        print(f"当前位置: {current_info.get('formatted_address', '未知')}")
    else:
        print("当前位置: 未初始化")
    
    # 更新到新位置（天安门坐标）
    new_longitude = 116.397428
    new_latitude = 39.90923
    
    print(f"\n更新位置到: ({new_longitude}, {new_latitude})")
    update_result = agent.update_location_context(new_longitude, new_latitude)
    
    if update_result.get("status"):
        print("✅ 位置更新成功")
        
        # 显示新位置信息
        new_context = agent.get_location_context()
        new_info = new_context.get("current_location_info", {})
        print(f"新位置: {new_info.get('formatted_address', '未知')}")
        
        nearby_pois = new_info.get("nearby_pois", [])
        if nearby_pois:
            print(f"附近POI:")
            for poi in nearby_pois:
                name = poi.get("name", "")
                distance = poi.get("distance", 0)
                print(f"  - {name} ({distance}米)")
    else:
        print(f"❌ 位置更新失败: {update_result.get('error', '未知错误')}")


def test_new_tools():
    """测试新增的工具"""
    print("\n\n🔧 测试新增的工具")
    print("=" * 50)
    
    agent = EnhancedTaxiAgent()
    
    # 测试经纬度转POI
    print("1. 测试经纬度转POI工具")
    test_input = f"经纬度{DEFAULT_LONGITUDE},{DEFAULT_LATITUDE}附近有什么？"
    try:
        response = agent.process_message(test_input, "tool_test_1")
        print(f"输入: {test_input}")
        print(f"回复: {response}")
    except Exception as e:
        print(f"错误: {e}")
    
    print("\n" + "-" * 30)
    
    # 测试POI推荐
    print("2. 测试POI推荐工具")
    test_input = "推荐一些北京上地星巴克附近的咖啡店"
    try:
        response = agent.process_message(test_input, "tool_test_2")
        print(f"输入: {test_input}")
        print(f"回复: {response}")
    except Exception as e:
        print(f"错误: {e}")


def test_main_agent_compatibility():
    """测试MainAgent兼容性"""
    print("\n\n🔄 测试MainAgent兼容性")
    print("=" * 50)
    
    # 使用MainAgent
    main_agent = MainAgent()
    
    test_cases = [
        "我在哪里？",
        "附近有什么好吃的？",
        "我要从这里打车到机场"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- MainAgent测试 {i}: {test_input} ---")
        try:
            response = main_agent.process_user_input(test_input)
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")


def main():
    """主测试函数"""
    print("🚗 位置上下文功能测试")
    print("测试默认经纬度转POI和环境信息集成")
    print("=" * 60)
    
    # 检查环境
    if not os.getenv("AMAP_API_KEY"):
        print("⚠️  警告: 未设置AMAP_API_KEY，地图功能可能不可用")
    
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  警告: 未设置BAILIAN_API_KEY，AI对话功能可能不可用")
    
    try:
        # 1. 测试位置上下文初始化
        agent = test_location_context_initialization()
        
        # 2. 测试位置上下文在对话中的应用
        test_location_context_in_conversation()
        
        # 3. 测试位置更新功能
        test_location_update()
        
        # 4. 测试新增工具
        test_new_tools()
        
        # 5. 测试MainAgent兼容性
        test_main_agent_compatibility()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        
        # 显示配置建议
        print("\n📝 配置建议:")
        print("1. 设置环境变量 DEFAULT_LONGITUDE 和 DEFAULT_LATITUDE 自定义默认位置")
        print("2. 设置环境变量 LOCATION_CONTEXT_ENABLED=false 禁用位置上下文")
        print("3. 确保 AMAP_API_KEY 和 BAILIAN_API_KEY 正确配置")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
