#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为模拟器使用示例（带API密钥配置）
展示如何正确配置和使用用户行为模拟器
"""

import os
from user_behavior_simulator import UserBehaviorSimulator


def setup_api_keys():
    """设置API密钥（示例）"""
    # 方法1: 直接设置环境变量（推荐）
    # os.environ["BAILIAN_API_KEY"] = "your_bailian_api_key_here"
    # os.environ["AMAP_API_KEY"] = "your_amap_api_key_here"
    
    # 方法2: 从配置文件读取
    # import json
    # with open('config.json', 'r') as f:
    #     config = json.load(f)
    #     os.environ["BAILIAN_API_KEY"] = config["bailian_api_key"]
    #     os.environ["AMAP_API_KEY"] = config["amap_api_key"]
    
    # 方法3: 从.env文件读取
    # from dotenv import load_dotenv
    # load_dotenv()
    
    print("请在运行前设置以下环境变量:")
    print("export BAILIAN_API_KEY='your_bailian_api_key'")
    print("export AMAP_API_KEY='your_amap_api_key'")


def example_single_conversation():
    """示例：单个对话模拟"""
    print("=== 单个对话模拟示例 ===")
    
    # 初始化模拟器
    simulator = UserBehaviorSimulator()
    
    # 创建会话
    session_id = simulator.generate_session_id()
    scenario_type = 1  # 首次使用用户
    simulator.assign_scenario_to_session(session_id, scenario_type)
    
    print(f"会话ID: {session_id}")
    print(f"用户类型: {simulator.user_scenarios[scenario_type]['name']}")
    
    # 模拟3轮对话
    context = ""
    for turn in range(1, 4):
        # 生成用户输入
        user_input = simulator.generate_user_input(session_id, context, turn)
        print(f"\n第{turn}轮:")
        print(f"用户: {user_input}")
        
        # 获取系统回复
        try:
            system_response = simulator.taxi_agent.process_user_input(user_input)
            print(f"系统: {system_response}")
        except Exception as e:
            print(f"系统错误: {e}")
            break
        
        # 更新上下文
        context = f"用户说：{user_input}\n系统回复：{system_response}"


def example_batch_testing():
    """示例：批量测试"""
    print("\n=== 批量测试示例 ===")
    
    simulator = UserBehaviorSimulator()
    
    # 生成测试场景
    print("生成测试场景...")
    test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=2)
    
    # 分析结果
    analysis = simulator.analyze_test_results(test_scenarios)
    
    # 打印报告
    simulator.print_analysis_report(analysis)
    
    # 保存结果
    filename = simulator.save_test_results(test_scenarios)
    print(f"\n结果已保存到: {filename}")
    
    return test_scenarios, analysis


def example_custom_scenario():
    """示例：自定义场景测试"""
    print("\n=== 自定义场景测试示例 ===")
    
    simulator = UserBehaviorSimulator()
    
    # 测试特定场景类型
    scenarios_to_test = [1, 3, 5]  # 首次使用、探索型、超出范围
    
    for scenario_type in scenarios_to_test:
        print(f"\n测试场景 {scenario_type}: {simulator.user_scenarios[scenario_type]['name']}")
        
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)
        
        # 模拟完整对话
        conversation = simulator.simulate_conversation(session_id, max_turns=3)
        
        # 显示结果
        for turn_log in conversation:
            print(f"  第{turn_log['turn']}轮 - 用户: {turn_log['user_input']}")
            print(f"           系统: {turn_log['system_response'][:50]}...")


def example_analysis_only():
    """示例：仅分析现有结果"""
    print("\n=== 结果分析示例 ===")
    
    # 假设我们有一些测试结果
    simulator = UserBehaviorSimulator()
    
    # 创建一些示例数据
    sample_scenarios = []
    for i in range(5):
        sample_scenarios.append({
            "session_id": f"sample_{i}",
            "scenario_type": (i % 5) + 1,
            "scenario_name": f"测试场景{i+1}",
            "conversation": [
                {
                    "turn": 1,
                    "user_input": f"测试输入{i+1}",
                    "system_response": "测试回复",
                    "timestamp": "2024-01-01T12:00:00"
                }
            ],
            "summary": {
                "total_turns": 1,
                "is_successful": i % 2 == 0,  # 模拟50%成功率
                "conversation_length": 20
            }
        })
    
    # 分析结果
    analysis = simulator.analyze_test_results(sample_scenarios)
    simulator.print_analysis_report(analysis)


def example_session_management():
    """示例：会话管理"""
    print("\n=== 会话管理示例 ===")
    
    simulator = UserBehaviorSimulator()
    
    # 创建多个会话
    sessions = []
    for scenario_type in range(1, 4):
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)
        sessions.append(session_id)
        
        print(f"创建会话: {session_id[:8]}... (场景{scenario_type})")
    
    # 查看会话信息
    print("\n会话详情:")
    for session_id in sessions:
        info = simulator.get_session_info(session_id)
        print(f"  {session_id[:8]}...: {info['scenario_name']}")
        print(f"    特征: {', '.join(info['characteristics'][:2])}...")


def main():
    """主函数"""
    print("🚗 用户行为模拟器使用示例")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  注意: 未设置BAILIAN_API_KEY，将使用预设问题模式")
        setup_api_keys()
        print()
    
    # 运行示例
    try:
        # 单个对话示例
        example_single_conversation()
        
        # 批量测试示例
        example_batch_testing()
        
        # 自定义场景示例
        example_custom_scenario()
        
        # 结果分析示例
        example_analysis_only()
        
        # 会话管理示例
        example_session_management()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
