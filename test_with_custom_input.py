#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义用户输入测试脚本
以用户第一句话和类型作为入参测试MainAgent集成
"""

import os
import sys
from typing import Dict, List, Tuple
from test_main_agent_integration import test_with_first_message_and_type, batch_test_with_messages


def demo_single_test():
    """演示单个测试用例"""
    print("🎯 单个测试用例演示")
    print("=" * 40)
    
    # 测试用例
    test_cases = [
        ("你好，我想打车", 1, "首次使用用户的典型开场白"),
        ("我要从康德大厦打车到太阳宫", 2, "有历史记录用户的明确需求"),
        ("附近有什么好玩的地方？", 3, "探索型用户的开放性问题"),
        ("今天天气真不错", 4, "随意聊天用户的闲聊"),
        ("帮我订个酒店", 5, "超出能力范围的请求")
    ]
    
    for first_message, scenario_type, description in test_cases:
        print(f"\n📝 测试描述: {description}")
        result = test_with_first_message_and_type(first_message, scenario_type)
        
        if result["success"]:
            print(f"✅ 测试成功")
            if "response_analysis" in result:
                analysis = result["response_analysis"]
                print(f"   回复相关性: {'是' if analysis['is_relevant'] else '否'}")
                print(f"   回复有用性: {'是' if analysis['is_helpful'] else '否'}")
                print(f"   场景适配性: {'是' if analysis['handles_scenario'] else '否'}")
        else:
            print(f"❌ 测试失败: {result.get('error', '未知错误')}")
        
        print("-" * 40)


def demo_batch_test():
    """演示批量测试"""
    print("\n🚀 批量测试演示")
    print("=" * 40)
    
    # 批量测试用例
    batch_cases = [
        # 首次使用用户的多种表达
        ("你好", 1),
        ("我想打车", 1),
        ("这个怎么用？", 1),
        ("能帮我叫车吗？", 1),
        
        # 有历史记录用户的多种表达
        ("还是去老地方", 2),
        ("从家里到公司", 2),
        ("我要舒适型车辆", 2),
        
        # 探索型用户的多种表达
        ("附近有什么？", 3),
        ("我想随便逛逛", 3),
        ("有什么推荐吗？", 3),
        
        # 随意聊天用户的多种表达
        ("心情不太好", 4),
        ("你觉得北京怎么样？", 4),
        ("能聊天吗？", 4),
        
        # 超出能力范围的多种表达
        ("查明天天气", 5),
        ("帮我翻译", 5),
        ("订个餐厅", 5),
    ]
    
    results = batch_test_with_messages(batch_cases)
    
    # 详细分析
    print("\n📊 详细分析:")
    successful_results = [r for r in results if r["success"]]
    
    if successful_results:
        print(f"成功测试数: {len(successful_results)}")
        
        # 分析回复质量
        relevant_count = sum(1 for r in successful_results 
                           if r.get("response_analysis", {}).get("is_relevant", False))
        helpful_count = sum(1 for r in successful_results 
                          if r.get("response_analysis", {}).get("is_helpful", False))
        
        print(f"相关回复数: {relevant_count}/{len(successful_results)}")
        print(f"有用回复数: {helpful_count}/{len(successful_results)}")
    
    return results


def interactive_test():
    """交互式测试"""
    print("\n🎮 交互式测试")
    print("=" * 40)
    print("请输入用户的第一句话和场景类型进行测试")
    print("场景类型: 1=首次使用, 2=有历史, 3=探索, 4=聊天, 5=超出范围")
    print("输入 'quit' 退出")
    
    while True:
        try:
            # 获取用户输入
            first_message = input("\n请输入用户第一句话: ").strip()
            if first_message.lower() == 'quit':
                break
            
            if not first_message:
                print("请输入有效的用户消息")
                continue
            
            scenario_type_input = input("请输入场景类型 (1-5): ").strip()
            try:
                scenario_type = int(scenario_type_input)
                if scenario_type not in range(1, 6):
                    print("场景类型必须是1-5之间的数字")
                    continue
            except ValueError:
                print("请输入有效的数字")
                continue
            
            # 执行测试
            print(f"\n🔍 测试中...")
            result = test_with_first_message_and_type(first_message, scenario_type)
            
            # 显示结果
            if result["success"]:
                print(f"\n✅ 测试结果:")
                print(f"场景: {result['scenario_name']}")
                print(f"用户: {result['user_input']}")
                print(f"系统: {result['system_response']}")
                
                if "response_analysis" in result:
                    analysis = result["response_analysis"]
                    print(f"\n📈 质量分析:")
                    print(f"  相关性: {'✅' if analysis['is_relevant'] else '❌'}")
                    print(f"  有用性: {'✅' if analysis['is_helpful'] else '❌'}")
                    print(f"  场景适配: {'✅' if analysis['handles_scenario'] else '❌'}")
                    if analysis['keywords_found']:
                        print(f"  关键词: {', '.join(analysis['keywords_found'])}")
            else:
                print(f"\n❌ 测试失败: {result.get('error', '未知错误')}")
                
        except KeyboardInterrupt:
            print("\n\n👋 测试被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")


def predefined_scenarios_test():
    """预定义场景测试"""
    print("\n📋 预定义场景测试")
    print("=" * 40)
    
    # 每种场景的典型用例
    scenarios = {
        1: [  # 首次使用用户
            "你好，我想打车",
            "这个系统怎么用？",
            "我要去机场",
            "能帮我叫车吗？",
            "我不太会用"
        ],
        2: [  # 有历史记录用户
            "还是去上次那个地方",
            "从康德大厦到太阳宫",
            "我要舒适型车辆",
            "和上次一样的路线",
            "老地方见"
        ],
        3: [  # 探索型用户
            "附近有什么好玩的？",
            "我想随便逛逛",
            "有什么推荐吗？",
            "这附近有什么？",
            "想看看周边"
        ],
        4: [  # 随意聊天用户
            "今天天气真好",
            "你能聊天吗？",
            "我心情不太好",
            "你觉得北京怎么样？",
            "想听个笑话"
        ],
        5: [  # 超出能力范围用户
            "帮我订个酒店",
            "查明天的天气",
            "我想买机票",
            "帮我找个餐厅",
            "能帮我翻译吗？"
        ]
    }
    
    all_results = []
    
    for scenario_type, messages in scenarios.items():
        print(f"\n测试场景 {scenario_type}:")
        scenario_results = []
        
        for message in messages:
            result = test_with_first_message_and_type(message, scenario_type)
            scenario_results.append(result)
            all_results.append(result)
            
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {message}")
        
        # 场景统计
        success_count = sum(1 for r in scenario_results if r["success"])
        print(f"  场景成功率: {success_count}/{len(scenario_results)} ({success_count/len(scenario_results)*100:.1f}%)")
    
    # 总体统计
    total_success = sum(1 for r in all_results if r["success"])
    print(f"\n📊 总体统计:")
    print(f"总测试数: {len(all_results)}")
    print(f"成功数: {total_success}")
    print(f"成功率: {total_success/len(all_results)*100:.1f}%")
    
    return all_results


def main():
    """主函数"""
    print("🚗 自定义用户输入测试工具")
    print("支持以用户第一句话和类型作为入参测试MainAgent")
    print("=" * 60)
    
    # 检查环境
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  注意: 未设置BAILIAN_API_KEY，将使用预设问题模式")
    
    print("\n请选择测试模式:")
    print("1. 单个测试演示")
    print("2. 批量测试演示") 
    print("3. 交互式测试")
    print("4. 预定义场景测试")
    print("5. 运行所有测试")
    
    choice = input("\n请输入选项 (1-5): ").strip()
    
    try:
        if choice == "1":
            demo_single_test()
        elif choice == "2":
            demo_batch_test()
        elif choice == "3":
            interactive_test()
        elif choice == "4":
            predefined_scenarios_test()
        elif choice == "5":
            print("🚀 运行所有测试...")
            demo_single_test()
            demo_batch_test()
            predefined_scenarios_test()
        else:
            print("无效选项")
            return False
        
        print("\n🎉 测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
