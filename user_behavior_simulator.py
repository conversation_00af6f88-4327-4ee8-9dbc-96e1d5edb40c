#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为模拟智能体
使用大语言模型模拟用户的行为，生成多轮的问题，用于测试系统的智能水平
"""

import os
import json
import uuid
import random
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from openai import OpenAI
from taxi_agent_system import EnhancedTaxiAgent


class UserBehaviorSimulator:
    """用户行为模拟器"""
    
    def __init__(self):
        # 百炼模型配置
        api_key = os.getenv("BAILIAN_API_KEY")
        if api_key:
            self.bailian_client = OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            self.bailian_model_name = "qwen-max"
        else:
            print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能将不可用")
            self.bailian_client = None
            self.bailian_model_name = None
        
        # 初始化打车系统
        self.taxi_agent = EnhancedTaxiAgent()
        
        # 用户场景定义
        self.user_scenarios = {
            1: {
                "name": "首次使用用户",
                "description": "首次使用这个系统，有明确目标就是需要打车但是每次输入的信息有限；不知道该怎么用这个系统",
                "characteristics": [
                    "不熟悉系统功能",
                    "信息提供不完整",
                    "需要引导和帮助",
                    "可能会问基础问题",
                    "对系统能力不了解"
                ]
            },
            2: {
                "name": "有历史记录用户",
                "description": "存在明确历史记录，有明确目标需要打车的",
                "characteristics": [
                    "熟悉系统操作",
                    "有明确的出行需求",
                    "可能提及历史记录",
                    "期望快速完成任务",
                    "对系统有一定信任"
                ]
            },
            3: {
                "name": "探索型用户",
                "description": "没有明确目的地，纯探索",
                "characteristics": [
                    "没有明确目标",
                    "想了解周边信息",
                    "可能问开放性问题",
                    "对推荐感兴趣",
                    "探索系统功能"
                ]
            },
            4: {
                "name": "随意聊天用户",
                "description": "不着边际",
                "characteristics": [
                    "话题跳跃",
                    "可能聊天气、心情等",
                    "不专注于打车需求",
                    "测试系统边界",
                    "随意性强"
                ]
            },
            5: {
                "name": "超出能力范围用户",
                "description": "能力范围以外",
                "characteristics": [
                    "提出系统无法处理的需求",
                    "可能询问其他服务",
                    "测试系统限制",
                    "期望更多功能",
                    "可能有不合理要求"
                ]
            }
        }
        
        # 会话记录
        self.sessions = {}
        self.session_scenarios = {}  # session_id -> scenario_type 的映射
        
    def generate_session_id(self) -> str:
        """生成新的会话ID"""
        return str(uuid.uuid4())
    
    def assign_scenario_to_session(self, session_id: str, scenario_type: int) -> None:
        """为会话分配用户场景"""
        if scenario_type not in self.user_scenarios:
            raise ValueError(f"无效的场景类型: {scenario_type}")
        
        self.session_scenarios[session_id] = scenario_type
        
        # 初始化会话记录
        if session_id not in self.sessions:
            self.sessions[session_id] = {
                "scenario_type": scenario_type,
                "scenario_name": self.user_scenarios[scenario_type]["name"],
                "conversation_history": [],
                "created_at": datetime.now().isoformat(),
                "turn_count": 0
            }
    
    def generate_user_input(self, session_id: str, context: str = "", turn_number: int = 1) -> str:
        """生成用户输入"""
        if not self.bailian_client:
            return self._generate_fallback_input(session_id, turn_number)
        
        scenario_type = self.session_scenarios.get(session_id)
        if not scenario_type:
            raise ValueError(f"会话 {session_id} 未分配场景类型")
        
        scenario = self.user_scenarios[scenario_type]
        
        # 构建提示词
        prompt = self._build_user_generation_prompt(scenario, context, turn_number)
        
        try:
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个用户行为模拟专家，能够根据不同用户场景生成真实的用户输入。'},
                    {'role': 'user', 'content': prompt}
                ],
                max_tokens=200,
                temperature=0.8,
                extra_body={
                    "enable_search": True
                }
            )
            
            user_input = response.choices[0].message.content.strip()
            
            # 记录到会话历史
            self.sessions[session_id]["conversation_history"].append({
                "turn": turn_number,
                "user_input": user_input,
                "timestamp": datetime.now().isoformat()
            })
            self.sessions[session_id]["turn_count"] = turn_number
            
            return user_input
            
        except Exception as e:
            print(f"生成用户输入失败: {e}")
            return self._generate_fallback_input(session_id, turn_number)
    
    def _build_user_generation_prompt(self, scenario: Dict, context: str, turn_number: int) -> str:
        """构建用户输入生成的提示词"""
        base_prompt = f"""
请模拟一个{scenario['name']}的用户行为，生成第{turn_number}轮的用户输入。

用户场景描述：{scenario['description']}

用户特征：
{chr(10).join([f"- {char}" for char in scenario['characteristics']])}

当前对话上下文：{context if context else "这是对话的开始"}

要求：
1. 生成的输入要符合该用户类型的特征
2. 语言要自然，像真实用户的表达
3. 考虑语音输入的特点（口语化、可能有停顿词）
4. 长度控制在1-3句话
5. 如果是首轮对话，生成开场白
6. 如果是后续轮次，要考虑上下文连贯性

只返回用户输入内容，不要其他解释。
"""
        
        # 根据轮次调整提示
        if turn_number == 1:
            base_prompt += "\n注意：这是首轮对话，用户刚开始使用系统。"
        else:
            base_prompt += f"\n注意：这是第{turn_number}轮对话，用户已经与系统有了{turn_number-1}轮交互。"
        
        return base_prompt
    
    def _generate_fallback_input(self, session_id: str, turn_number: int) -> str:
        """回退的用户输入生成（不依赖大模型）"""
        scenario_type = self.session_scenarios.get(session_id, 1)
        
        fallback_inputs = {
            1: [  # 首次使用用户
                "你好，我想打车",
                "这个怎么用啊？",
                "我要去机场",
                "能帮我叫车吗？",
                "我不太会用这个系统"
            ],
            2: [  # 有历史记录用户
                "我要从家里去公司",
                "帮我叫一辆车到老地方",
                "还是去上次那个地方",
                "从康德大厦到太阳宫",
                "我要舒适型车辆"
            ],
            3: [  # 探索型用户
                "附近有什么好玩的地方吗？",
                "我想随便逛逛",
                "有什么推荐的吗？",
                "这附近有什么？",
                "我想看看周边"
            ],
            4: [  # 随意聊天用户
                "今天天气真好",
                "你能聊天吗？",
                "我心情不太好",
                "你觉得北京怎么样？",
                "我想听个笑话"
            ],
            5: [  # 超出能力范围用户
                "帮我订个酒店",
                "查一下明天的天气",
                "我想买机票",
                "帮我找个餐厅",
                "能帮我翻译吗？"
            ]
        }
        
        inputs = fallback_inputs.get(scenario_type, fallback_inputs[1])
        selected_input = random.choice(inputs)
        
        # 记录到会话历史
        self.sessions[session_id]["conversation_history"].append({
            "turn": turn_number,
            "user_input": selected_input,
            "timestamp": datetime.now().isoformat(),
            "generated_by": "fallback"
        })
        self.sessions[session_id]["turn_count"] = turn_number
        
        return selected_input

    def simulate_conversation(self, session_id: str, max_turns: int = 5) -> List[Dict]:
        """模拟完整的对话过程"""
        conversation_log = []
        context = ""

        for turn in range(1, max_turns + 1):
            # 生成用户输入
            user_input = self.generate_user_input(session_id, context, turn)

            # 获取系统回复
            system_response = self.taxi_agent.process_message(user_input, session_id)

            # 记录对话
            turn_log = {
                "turn": turn,
                "user_input": user_input,
                "system_response": system_response,
                "timestamp": datetime.now().isoformat(),
                "scenario_type": self.session_scenarios[session_id],
                "scenario_name": self.user_scenarios[self.session_scenarios[session_id]]["name"]
            }
            conversation_log.append(turn_log)

            # 更新上下文
            context = f"用户说：{user_input}\n系统回复：{system_response}"

            # 检查是否应该结束对话
            if self._should_end_conversation(system_response, turn):
                break

        return conversation_log

    def _should_end_conversation(self, system_response: str, turn: int) -> bool:
        """判断是否应该结束对话"""
        # 如果系统成功完成了打车任务，可以结束
        success_indicators = ["成功", "已为您", "车辆", "司机", "预计"]
        if any(indicator in system_response for indicator in success_indicators):
            return True

        # 如果达到最大轮次
        if turn >= 5:
            return True

        # 如果系统明确表示无法处理
        failure_indicators = ["抱歉", "无法", "不支持", "超出范围"]
        if any(indicator in system_response for indicator in failure_indicators):
            return True

        return False

    def generate_test_scenarios(self, num_sessions_per_type: int = 2) -> List[Dict]:
        """生成测试场景"""
        test_scenarios = []

        for scenario_type in range(1, 6):  # 5种用户类型
            for i in range(num_sessions_per_type):
                session_id = self.generate_session_id()
                self.assign_scenario_to_session(session_id, scenario_type)

                # 模拟对话
                conversation = self.simulate_conversation(session_id)

                test_scenarios.append({
                    "session_id": session_id,
                    "scenario_type": scenario_type,
                    "scenario_name": self.user_scenarios[scenario_type]["name"],
                    "conversation": conversation,
                    "summary": self._generate_conversation_summary(conversation)
                })

        return test_scenarios

    def _generate_conversation_summary(self, conversation: List[Dict]) -> Dict:
        """生成对话摘要"""
        total_turns = len(conversation)
        user_inputs = [turn["user_input"] for turn in conversation]
        system_responses = [turn["system_response"] for turn in conversation]

        # 分析对话结果
        last_response = system_responses[-1] if system_responses else ""
        success_indicators = ["成功", "已为您", "车辆", "司机", "预计"]
        is_successful = any(indicator in last_response for indicator in success_indicators)

        return {
            "total_turns": total_turns,
            "is_successful": is_successful,
            "last_response": last_response,
            "conversation_length": sum(len(inp) + len(resp) for inp, resp in zip(user_inputs, system_responses))
        }

    def save_test_results(self, test_scenarios: List[Dict], filename: str = None) -> str:
        """保存测试结果"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"user_behavior_test_{timestamp}.json"

        # 添加元数据
        test_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "total_scenarios": len(test_scenarios),
                "scenario_types": list(self.user_scenarios.keys()),
                "generator_version": "1.0"
            },
            "scenarios": test_scenarios
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)

        return filename

    def analyze_test_results(self, test_scenarios: List[Dict]) -> Dict:
        """分析测试结果"""
        analysis = {
            "overall_stats": {
                "total_scenarios": len(test_scenarios),
                "successful_scenarios": 0,
                "average_turns": 0,
                "total_turns": 0
            },
            "by_scenario_type": {}
        }

        # 按场景类型分析
        for scenario_type in range(1, 6):
            type_scenarios = [s for s in test_scenarios if s["scenario_type"] == scenario_type]
            if type_scenarios:
                successful = sum(1 for s in type_scenarios if s["summary"]["is_successful"])
                total_turns = sum(s["summary"]["total_turns"] for s in type_scenarios)

                analysis["by_scenario_type"][scenario_type] = {
                    "scenario_name": self.user_scenarios[scenario_type]["name"],
                    "total_scenarios": len(type_scenarios),
                    "successful_scenarios": successful,
                    "success_rate": successful / len(type_scenarios) if type_scenarios else 0,
                    "average_turns": total_turns / len(type_scenarios) if type_scenarios else 0,
                    "total_turns": total_turns
                }

        # 整体统计
        analysis["overall_stats"]["successful_scenarios"] = sum(
            1 for s in test_scenarios if s["summary"]["is_successful"]
        )
        analysis["overall_stats"]["total_turns"] = sum(
            s["summary"]["total_turns"] for s in test_scenarios
        )
        analysis["overall_stats"]["average_turns"] = (
            analysis["overall_stats"]["total_turns"] / len(test_scenarios)
            if test_scenarios else 0
        )
        analysis["overall_stats"]["success_rate"] = (
            analysis["overall_stats"]["successful_scenarios"] / len(test_scenarios)
            if test_scenarios else 0
        )

        return analysis

    def print_analysis_report(self, analysis: Dict) -> None:
        """打印分析报告"""
        print("\n" + "="*60)
        print("用户行为模拟测试分析报告")
        print("="*60)

        # 整体统计
        overall = analysis["overall_stats"]
        print(f"\n整体统计:")
        print(f"  总场景数: {overall['total_scenarios']}")
        print(f"  成功场景数: {overall['successful_scenarios']}")
        print(f"  成功率: {overall['success_rate']:.1%}")
        print(f"  平均轮次: {overall['average_turns']:.1f}")
        print(f"  总轮次: {overall['total_turns']}")

        # 按场景类型统计
        print(f"\n按场景类型统计:")
        for scenario_type, stats in analysis["by_scenario_type"].items():
            print(f"\n  {scenario_type}. {stats['scenario_name']}:")
            print(f"    场景数: {stats['total_scenarios']}")
            print(f"    成功数: {stats['successful_scenarios']}")
            print(f"    成功率: {stats['success_rate']:.1%}")
            print(f"    平均轮次: {stats['average_turns']:.1f}")

        print("\n" + "="*60)

    def get_session_info(self, session_id: str) -> Dict:
        """获取会话信息"""
        if session_id not in self.sessions:
            return {"error": "会话不存在"}

        session = self.sessions[session_id]
        scenario_type = session["scenario_type"]

        return {
            "session_id": session_id,
            "scenario_type": scenario_type,
            "scenario_name": session["scenario_name"],
            "scenario_description": self.user_scenarios[scenario_type]["description"],
            "characteristics": self.user_scenarios[scenario_type]["characteristics"],
            "created_at": session["created_at"],
            "turn_count": session["turn_count"],
            "conversation_history": session["conversation_history"]
        }


def demo_user_behavior_simulator():
    """演示用户行为模拟器"""
    print("=== 用户行为模拟器演示 ===")

    # 初始化模拟器
    simulator = UserBehaviorSimulator()

    # 演示单个场景
    print("\n1. 演示单个用户场景:")
    session_id = simulator.generate_session_id()
    scenario_type = 1  # 首次使用用户
    simulator.assign_scenario_to_session(session_id, scenario_type)

    print(f"会话ID: {session_id}")
    print(f"用户类型: {simulator.user_scenarios[scenario_type]['name']}")
    print(f"场景描述: {simulator.user_scenarios[scenario_type]['description']}")

    # 模拟3轮对话
    print("\n对话过程:")
    context = ""
    for turn in range(1, 4):
        user_input = simulator.generate_user_input(session_id, context, turn)
        system_response = simulator.taxi_agent.process_message(user_input, session_id)

        print(f"\n第{turn}轮:")
        print(f"用户: {user_input}")
        print(f"系统: {system_response}")

        context = f"用户说：{user_input}\n系统回复：{system_response}"

    # 演示批量测试
    print("\n\n2. 演示批量测试:")
    test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=1)

    # 分析结果
    analysis = simulator.analyze_test_results(test_scenarios)
    simulator.print_analysis_report(analysis)

    # 保存结果
    filename = simulator.save_test_results(test_scenarios)
    print(f"\n测试结果已保存到: {filename}")

    return simulator, test_scenarios


def generate_first_round_questions():
    """生成首轮问题示例"""
    print("\n=== 生成首轮问题示例 ===")

    simulator = UserBehaviorSimulator()

    print("\n各类用户的首轮问题示例:")
    for scenario_type in range(1, 6):
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)

        scenario_name = simulator.user_scenarios[scenario_type]["name"]
        print(f"\n{scenario_type}. {scenario_name}:")

        # 生成3个首轮问题示例
        for i in range(3):
            user_input = simulator.generate_user_input(session_id, "", 1)
            print(f"   示例{i+1}: {user_input}")


def test_specific_scenario(scenario_type: int, max_turns: int = 5):
    """测试特定场景"""
    print(f"\n=== 测试场景 {scenario_type} ===")

    simulator = UserBehaviorSimulator()
    session_id = simulator.generate_session_id()
    simulator.assign_scenario_to_session(session_id, scenario_type)

    scenario_name = simulator.user_scenarios[scenario_type]["name"]
    print(f"用户类型: {scenario_name}")
    print(f"会话ID: {session_id}")

    # 模拟完整对话
    conversation = simulator.simulate_conversation(session_id, max_turns)

    print(f"\n完整对话记录 (共{len(conversation)}轮):")
    for turn_log in conversation:
        print(f"\n第{turn_log['turn']}轮:")
        print(f"用户: {turn_log['user_input']}")
        print(f"系统: {turn_log['system_response']}")

    # 显示摘要
    summary = simulator._generate_conversation_summary(conversation)
    print(f"\n对话摘要:")
    print(f"  总轮次: {summary['total_turns']}")
    print(f"  是否成功: {'是' if summary['is_successful'] else '否'}")
    print(f"  对话长度: {summary['conversation_length']} 字符")

    return conversation


if __name__ == "__main__":
    # 运行演示
    print("用户行为模拟智能体")
    print("支持5种用户场景的模拟测试")

    # 演示功能
    demo_user_behavior_simulator()

    # 生成首轮问题示例
    generate_first_round_questions()

    # 测试特定场景
    print("\n" + "="*60)
    test_specific_scenario(1)  # 测试首次使用用户
