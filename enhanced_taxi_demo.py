#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版打车系统演示
展示集成了高德地图工具和打车服务的完整系统
"""

import json
import os
from taxi_agent_system import EnhancedTaxiAgent


def demo_function_calling_tools():
    """演示Function Calling工具定义"""
    print("=== Function Calling 工具定义 ===")
    
    agent = EnhancedTaxiAgent()
    
    print("系统提供的工具:")
    for i, tool in enumerate(agent.tools, 1):
        func_info = tool['function']
        print(f"\n{i}. {func_info['name']}")
        print(f"   描述: {func_info['description']}")
        
        # 显示参数
        params = func_info['parameters']
        required = params.get('required', [])
        properties = params.get('properties', {})
        
        print(f"   必需参数: {required}")
        print("   参数详情:")
        for param_name, param_info in properties.items():
            required_mark = " (必需)" if param_name in required else " (可选)"
            print(f"     - {param_name}{required_mark}: {param_info.get('description', 'N/A')}")


def demo_taxi_service_integration():
    """演示打车服务集成"""
    print("\n=== 打车服务集成演示 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试打车服务
    print("调用打车服务: 从康德大厦到太阳宫")
    
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "demo_session"
        )
        
        print("\n统一格式化后的结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 解析结果
        if result.get("status"):
            print(f"\n✅ 打车服务调用成功!")
            print(f"📍 出发地: {result['details']['pickup_name']}")
            print(f"📍 目的地: {result['details']['destination_name']}")
            print(f"💬 系统回复: {result['message']}")
            print(f"⏱️ 耗时: {result['time_cost']:.2f}秒")
        else:
            print(f"\n❌ 打车服务调用失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"\n❌ 调用出错: {e}")


def demo_format_comparison():
    """演示输出格式对比"""
    print("\n=== 输出格式对比 ===")
    
    # 原始格式示例
    original_format = {
        'action_ret_msg': '康得大厦到太阳宫(地铁站)，可以么？',
        'status': 1,
        'err_msg': '',
        'err_code': 'success',
        'detail_info': {},
        'params': {
            'output': '康得大厦到太阳宫(地铁站)，可以么？',
            'pickup_name': '康得大厦',
            'pickup_l': '116.304228',
            'pickup_r': '40.042815',
            'dest_name': '太阳宫(地铁站)',
            'dest_l': '116.448213',
            'dest_r': '39.973816',
            'sub_id': 2
        },
        'time_cost': 0.8740000000000001
    }
    
    print("原始 call_taxi_service 输出格式:")
    print(json.dumps(original_format, indent=2, ensure_ascii=False))
    
    # 格式化后的格式
    agent = EnhancedTaxiAgent()
    formatted_result = agent._format_taxi_service_result(original_format)
    
    print("\n统一格式化后的输出:")
    print(json.dumps(formatted_result, indent=2, ensure_ascii=False))
    
    print("\n格式改进说明:")
    print("1. status: 统一为布尔值 (true/false)")
    print("2. message: 提取主要回复信息")
    print("3. details: 结构化的详细信息")
    print("4. 位置信息: 分离经纬度为 longitude/latitude")
    print("5. raw_data: 保留原始数据用于调试")


def demo_amap_tools_schema():
    """演示高德地图工具的Function Calling定义"""
    print("\n=== 高德地图工具 Function Calling 定义 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 筛选高德地图相关工具
    amap_tools = [tool for tool in agent.tools if tool['function']['name'].startswith('mcp_')]
    
    for tool in amap_tools:
        func_info = tool['function']
        print(f"\n工具名称: {func_info['name']}")
        print(f"功能描述: {func_info['description']}")
        
        # 生成调用示例
        if func_info['name'] == 'mcp_geocode_address':
            example = {
                "address": "康德大厦",
                "city": "北京"
            }
        elif func_info['name'] == 'mcp_get_city_code':
            example = {
                "city_name": "北京"
            }
        elif func_info['name'] == 'mcp_search_poi':
            example = {
                "keyword": "星巴克",
                "city": "北京"
            }
        else:
            example = {}
        
        print(f"调用示例: {json.dumps(example, ensure_ascii=False)}")


def demo_complete_workflow():
    """演示完整的工作流程"""
    print("\n=== 完整工作流程演示 ===")
    
    print("场景: 用户想从康德大厦打车到太阳宫")
    print("\n步骤1: 可以先查询地点信息 (如果需要)")
    print("步骤2: 调用打车服务")
    print("步骤3: 返回统一格式的结果")
    
    agent = EnhancedTaxiAgent()
    
    # 模拟完整流程
    print("\n执行打车服务...")
    
    try:
        # 直接调用打车服务
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": "舒适型"
            },
            "workflow_demo"
        )
        
        print("✅ 工作流程完成!")
        print(f"📱 用户将收到: {result.get('message', '无回复')}")
        print(f"🚗 车辆偏好: 舒适型")
        print(f"⏱️ 处理时间: {result.get('time_cost', 0):.2f}秒")
        
    except Exception as e:
        print(f"❌ 工作流程失败: {e}")


def demo_api_requirements():
    """演示API要求和配置"""
    print("\n=== API 配置要求 ===")
    
    print("1. 高德地图API:")
    print("   - 环境变量: AMAP_API_KEY")
    print("   - 获取地址: https://console.amap.com/")
    print("   - 需要开通: Web服务API")
    
    print("\n2. 百炼AI模型:")
    print("   - 环境变量: BAILIAN_API_KEY")
    print("   - 用于AI对话和Function Calling")
    
    print("\n3. 打车服务:")
    print("   - 已集成langflow_api_V4")
    print("   - 无需额外配置")
    
    # 检查当前配置状态
    print("\n当前配置状态:")
    amap_key = os.getenv("AMAP_API_KEY")
    bailian_key = os.getenv("BAILIAN_API_KEY")
    
    print(f"   AMAP_API_KEY: {'✅ 已配置' if amap_key else '❌ 未配置'}")
    print(f"   BAILIAN_API_KEY: {'✅ 已配置' if bailian_key else '❌ 未配置'}")


def main():
    """主演示函数"""
    print("增强版打车系统演示")
    print("=" * 60)
    
    # 1. 演示Function Calling工具定义
    demo_function_calling_tools()
    
    # 2. 演示高德地图工具定义
    demo_amap_tools_schema()
    
    # 3. 演示打车服务集成
    demo_taxi_service_integration()
    
    # 4. 演示输出格式对比
    demo_format_comparison()
    
    # 5. 演示完整工作流程
    demo_complete_workflow()
    
    # 6. 演示API配置要求
    demo_api_requirements()
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("\n主要特性:")
    print("✅ 集成高德地图工具 (地理编码、城市代码、POI搜索)")
    print("✅ 集成打车服务 (call_taxi_service)")
    print("✅ 统一输出格式")
    print("✅ Function Calling 支持")
    print("✅ StateManager 状态管理")
    print("✅ 错误处理和日志记录")


if __name__ == "__main__":
    main()
