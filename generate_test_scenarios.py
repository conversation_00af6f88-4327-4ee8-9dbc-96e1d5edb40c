import json
from uuid import uuid4
from datetime import datetime

def generate_scenario_1():
    """首次使用系统，目标明确但信息有限"""
    session_id = str(uuid4())
    scenario = [
        {
            "session_id": session_id,
            "scenario_type": 1,
            "timestamp": datetime.now().isoformat(),
            "user_input": "我想打车",
            "expected_agent_response": "请问您要去哪里？"
        },
        {
            "session_id": session_id,
            "scenario_type": 1,
            "timestamp": datetime.now().isoformat(),
            "user_input": "去...呃...",
            "expected_agent_response": "您可以告诉我大概的区域或地标吗？"
        },
        {
            "session_id": session_id,
            "scenario_type": 1,
            "timestamp": datetime.now().isoformat(),
            "user_input": "中关村那边",
            "expected_agent_response": "好的，中关村附近。请问具体地址是？比如大厦名称或地铁站"
        }
    ]
    return scenario

def generate_scenario_2():
    """有明确历史记录的目标"""
    session_id = str(uuid4())
    scenario = [
        {
            "session_id": session_id,
            "scenario_type": 2,
            "timestamp": datetime.now().isoformat(),
            "user_input": "叫车去公司",
            "expected_agent_response": "检测到您常用目的地：北京科技大厦。要前往这里吗？"
        },
        {
            "session_id": session_id,
            "scenario_type": 2,
            "timestamp": datetime.now().isoformat(),
            "user_input": "对",
            "expected_agent_response": "已为您预约前往北京科技大厦的车辆，预计3分钟后到达"
        }
    ]
    return scenario

def generate_scenario_3():
    """无明确目的地的探索"""
    session_id = str(uuid4())
    scenario = [
        {
            "session_id": session_id,
            "scenario_type": 3,
            "timestamp": datetime.now().isoformat(),
            "user_input": "附近有什么好玩的地方吗？",
            "expected_agent_response": "根据您的位置，推荐以下地点：1. 朝阳公园 2. 798艺术区 3. 三里屯。需要我帮您叫车前往吗？"
        },
        {
            "session_id": session_id,
            "scenario_type": 3,
            "timestamp": datetime.now().isoformat(),
            "user_input": "哪个最近？",
            "expected_agent_response": "朝阳公园距离您最近，约2公里。要前往吗？"
        }
    ]
    return scenario

def generate_scenario_4():
    """不着边际的对话"""
    session_id = str(uuid4())
    scenario = [
        {
            "session_id": session_id,
            "scenario_type": 4,
            "timestamp": datetime.now().isoformat(),
            "user_input": "今天天气怎么样？",
            "expected_agent_response": "当前天气晴朗，25度。需要出行服务吗？"
        },
        {
            "session_id": session_id,
            "scenario_type": 4,
            "timestamp": datetime.now().isoformat(),
            "user_input": "你觉得人类会移民火星吗？",
            "expected_agent_response": "这是个有趣的问题。目前我专注于出行服务，需要帮您叫车吗？"
        }
    ]
    return scenario

def generate_scenario_5():
    """能力范围外的请求"""
    session_id = str(uuid4())
    scenario = [
        {
            "session_id": session_id,
            "scenario_type": 5,
            "timestamp": datetime.now().isoformat(),
            "user_input": "帮我写份商业计划书",
            "expected_agent_response": "抱歉，我目前专注于出行服务。需要帮您叫车吗？"
        },
        {
            "session_id": session_id,
            "scenario_type": 5,
            "timestamp": datetime.now().isoformat(),
            "user_input": "那帮我订张机票",
            "expected_agent_response": "目前仅支持本地出行服务。需要帮您叫车吗？"
        }
    ]
    return scenario

def save_to_jsonl(scenarios, filename="test_dialogues.jsonl"):
    with open(filename, "a", encoding="utf-8") as f:
        for item in scenarios:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")

if __name__ == "__main__":
    # 清空或创建测试文件
    open("test_dialogues.jsonl", "w", encoding="utf-8").close()
    
    # 生成并保存所有场景
    save_to_jsonl(generate_scenario_1())
    save_to_jsonl(generate_scenario_2())
    save_to_jsonl(generate_scenario_3())
    save_to_jsonl(generate_scenario_4())
    save_to_jsonl(generate_scenario_5())
    
    print("已生成5种测试场景并保存到test_dialogues.jsonl")
