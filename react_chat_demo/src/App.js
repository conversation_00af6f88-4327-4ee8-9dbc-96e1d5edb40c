import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, MapPin, Car, Clock, User, Bot, Settings, BarChart3 } from 'lucide-react';
import ChatMessage from './components/ChatMessage';
import SystemStats from './components/SystemStats';
import ExampleQueries from './components/ExampleQueries';
import { v4 as uuidv4 } from 'uuid';

// 样式组件
const AppContainer = styled.div`
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;

const Sidebar = styled(motion.div)`
  width: 320px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
`;

const Header = styled.div`
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 15px;
`;

const Title = styled.h1`
  color: white;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
`;

const Subtitle = styled.p`
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
`;

const ChatContainer = styled.div`
  flex: 1;
  padding: 20px 30px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const InputContainer = styled.div`
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
`;

const InputWrapper = styled.div`
  display: flex;
  gap: 10px;
  align-items: flex-end;
`;

const TextInput = styled.textarea`
  flex: 1;
  padding: 12px 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 14px;
  resize: none;
  min-height: 44px;
  max-height: 120px;
  outline: none;
  transition: all 0.2s ease;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  &::placeholder {
    color: #999;
  }
`;

const SendButton = styled(motion.button)`
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: ${props => props.status === 'connected' ? '#10b981' : '#ef4444'};
  color: white;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 20px;
`;

const SectionTitle = styled.h3`
  color: #333;
  margin: 20px 0 10px 0;
  font-size: 16px;
  font-weight: 600;
`;

function App() {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => uuidv4());
  const [systemStatus, setSystemStatus] = useState('connected');
  const [stats, setStats] = useState({
    totalMessages: 0,
    successfulRequests: 0,
    failedRequests: 0,
    startTime: new Date()
  });
  
  const chatContainerRef = useRef(null);
  const inputRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息到后端
  const sendMessage = async (text) => {
    if (!text.trim()) return;

    const userMessage = {
      id: uuidv4(),
      role: 'user',
      content: text,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // 调用后端API
      const response = await fetch('http://localhost:8000/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: text,
          session_id: sessionId
        })
      });

      if (response.ok) {
        const data = await response.json();

        const assistantMessage = {
          id: uuidv4(),
          role: 'assistant',
          content: data.response,
          timestamp: new Date(),
          data: data.additional_data
        };

        setMessages(prev => [...prev, assistantMessage]);
        setStats(prev => ({
          ...prev,
          totalMessages: prev.totalMessages + 1,
          successfulRequests: prev.successfulRequests + 1
        }));
        setSystemStatus('connected');
      } else {
        throw new Error(`API请求失败: ${response.status}`);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      
      const errorMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: '抱歉，我现在无法处理您的请求。请稍后再试。',
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
      setStats(prev => ({
        ...prev,
        totalMessages: prev.totalMessages + 1,
        failedRequests: prev.failedRequests + 1
      }));
      setSystemStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理输入
  const handleSubmit = (e) => {
    e.preventDefault();
    sendMessage(inputValue);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // 示例查询
  const exampleQueries = [
    "西湖在哪里？",
    "帮我查一下杭州的城市代码",
    "北京有哪些星巴克？",
    "我要从康德大厦打车到太阳宫",
    "从北京站到首都机场，要舒适型车辆"
  ];

  const handleExampleClick = (query) => {
    setInputValue(query);
    inputRef.current?.focus();
  };

  return (
    <AppContainer>
      <Sidebar
        initial={{ x: -320 }}
        animate={{ x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <StatusIndicator status={systemStatus}>
          <div style={{ 
            width: 8, 
            height: 8, 
            borderRadius: '50%', 
            backgroundColor: 'currentColor' 
          }} />
          {systemStatus === 'connected' ? '系统已就绪' : '系统异常'}
        </StatusIndicator>

        <SystemStats stats={stats} />
        
        <SectionTitle>💡 示例查询</SectionTitle>
        <ExampleQueries 
          queries={exampleQueries}
          onQueryClick={handleExampleClick}
        />

        <SectionTitle>🎯 支持功能</SectionTitle>
        <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.6' }}>
          <div style={{ marginBottom: '8px' }}>🗺️ 地点查询和坐标转换</div>
          <div style={{ marginBottom: '8px' }}>🏙️ 城市信息和代码查询</div>
          <div style={{ marginBottom: '8px' }}>📍 POI搜索和商户查找</div>
          <div style={{ marginBottom: '8px' }}>🚗 智能打车和价格估算</div>
        </div>
      </Sidebar>

      <MainContent>
        <Header>
          <Car size={32} color="white" />
          <div>
            <Title>智能打车助手</Title>
            <Subtitle>基于高德地图API和AI技术的智能打车服务</Subtitle>
          </div>
        </Header>

        <ChatContainer ref={chatContainerRef}>
          <AnimatePresence>
            {messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
          </AnimatePresence>
          
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '10px',
                color: 'rgba(255, 255, 255, 0.8)'
              }}
            >
              <Bot size={20} />
              <span>正在思考...</span>
            </motion.div>
          )}
        </ChatContainer>

        <InputContainer>
          <form onSubmit={handleSubmit}>
            <InputWrapper>
              <TextInput
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="请输入您的问题..."
                disabled={isLoading}
              />
              <SendButton
                type="submit"
                disabled={isLoading || !inputValue.trim()}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Send size={20} />
              </SendButton>
            </InputWrapper>
          </form>
        </InputContainer>
      </MainContent>
    </AppContainer>
  );
}

export default App;
