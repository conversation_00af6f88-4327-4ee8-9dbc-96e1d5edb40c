#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数测试文件
专门测试高德地图工具和打车服务，不依赖AI模型
"""

import json
import time
from taxi_agent_system import EnhancedTaxiAgent
from langflow_api_V4 import call_taxi_service
from amap_mcp_tools import mcp_geocode_address, mcp_get_city_code, mcp_search_poi


def test_call_taxi_service_direct():
    """直接测试 call_taxi_service 函数"""
    print("=== 直接测试 call_taxi_service 函数 ===")
    
    try:
        result = call_taxi_service(
            start_place="康德大厦",
            end_place="太阳",
            car_prefer=""
        )
        
        print("原始输出格式:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_format_taxi_result():
    """测试打车服务结果格式化"""
    print("\n=== 测试打车服务结果格式化 ===")
    
    # 模拟原始结果（基于用户提供的格式）
    mock_result = {
        'action_ret_msg': '康得大厦到太阳宫(地铁站)，可以么？',
        'status': 1,
        'err_msg': '',
        'err_code': 'success',
        'detail_info': {},
        'params': {
            'output': '康得大厦到太阳宫(地铁站)，可以么？',
            'pickup_name': '康得大厦',
            'pickup_l': '116.304228',
            'pickup_r': '40.042815',
            'dest_name': '太阳宫(地铁站)',
            'dest_l': '116.448213',
            'dest_r': '39.973816',
            'sub_id': 2
        },
        'time_cost': 0.8740000000000001
    }
    
    try:
        agent = EnhancedTaxiAgent()
        formatted_result = agent._format_taxi_service_result(mock_result)
        
        print("格式化后的结果:")
        print(json.dumps(formatted_result, indent=2, ensure_ascii=False))
        
        return formatted_result
    except Exception as e:
        print(f"格式化测试失败: {e}")
        return None


def test_amap_tools_direct():
    """直接测试高德地图工具"""
    print("\n=== 直接测试高德地图工具 ===")
    
    # 测试地理编码
    print("1. 测试地理编码:")
    try:
        result = mcp_geocode_address("康德大厦", "北京")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"地理编码测试失败: {e}")
    
    # 测试城市代码
    print("\n2. 测试城市代码:")
    try:
        result = mcp_get_city_code("北京")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"城市代码测试失败: {e}")
    
    # 测试POI搜索
    print("\n3. 测试POI搜索:")
    try:
        result = mcp_search_poi("星巴克", "北京")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"POI搜索测试失败: {e}")


def test_integrated_functions():
    """测试集成的函数调用"""
    print("\n=== 测试集成的函数调用 ===")
    
    try:
        agent = EnhancedTaxiAgent()
        
        # 测试高德地图工具
        print("1. 测试集成的地理编码:")
        result = agent._execute_function(
            "mcp_geocode_address",
            {"address": "康德大厦", "city": "北京"},
            "test_session"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        print("\n2. 测试集成的打车服务:")
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "test_session"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"集成函数测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_function_calling_schema():
    """测试Function Calling工具定义"""
    print("\n=== 测试Function Calling工具定义 ===")
    
    try:
        agent = EnhancedTaxiAgent()
        
        print("可用工具:")
        for i, tool in enumerate(agent.tools, 1):
            print(f"{i}. {tool['function']['name']}")
            print(f"   描述: {tool['function']['description']}")
            print(f"   必需参数: {tool['function']['parameters'].get('required', [])}")
            
            # 显示参数详情
            properties = tool['function']['parameters'].get('properties', {})
            if properties:
                print("   参数详情:")
                for param_name, param_info in properties.items():
                    print(f"     - {param_name}: {param_info.get('description', 'N/A')}")
            print()
            
    except Exception as e:
        print(f"工具定义测试失败: {e}")


def compare_output_formats():
    """比较输出格式"""
    print("\n=== 比较输出格式 ===")
    
    print("原始 call_taxi_service 输出格式:")
    original_result = test_call_taxi_service_direct()
    
    if original_result:
        print("\n统一格式化后的输出:")
        formatted_result = test_format_taxi_result()
        
        print("\n格式对比:")
        print("原始格式字段:", list(original_result.keys()) if original_result else "无")
        print("统一格式字段:", list(formatted_result.keys()) if formatted_result else "无")


def main():
    """主测试函数"""
    print("工具函数测试")
    print("=" * 60)
    
    # 1. 测试Function Calling工具定义
    test_function_calling_schema()
    
    # 2. 直接测试高德地图工具
    test_amap_tools_direct()
    
    # 3. 直接测试打车服务
    print("\n" + "=" * 60)
    test_call_taxi_service_direct()
    
    # 4. 测试结果格式化
    test_format_taxi_result()
    
    # 5. 测试集成函数
    test_integrated_functions()
    
    # 6. 比较输出格式
    compare_output_formats()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
