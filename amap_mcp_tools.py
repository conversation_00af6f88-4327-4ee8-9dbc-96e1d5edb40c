"""
高德地图MCP工具集
提供地点到经纬度和地点到城市ID的转换功能
"""

import requests
import json
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class AmapMCPTools:
    """高德地图MCP工具类"""
    
    def __init__(self, api_key: str = None):
        """
        初始化高德地图工具
        
        Args:
            api_key: 高德地图API密钥，如果不提供则从环境变量AMAP_API_KEY获取
        """
        self.api_key = api_key or os.getenv("AMAP_API_KEY")
        if not self.api_key:
            raise ValueError("请提供高德地图API密钥，可通过参数或环境变量AMAP_API_KEY设置")
        
        # 高德地图API基础URL
        self.base_url = "https://restapi.amap.com/v3"
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    def geocode_address(self, address: str, city: str = None) -> Dict:
        """
        地点名称转换为经纬度坐标（地理编码）
        
        Args:
            address: 地点名称或地址
            city: 城市名称，可选，用于提高搜索精度
            
        Returns:
            Dict: 包含经纬度信息的字典
            {
                "status": bool,
                "data": {
                    "longitude": float,
                    "latitude": float,
                    "formatted_address": str,
                    "province": str,
                    "city": str,
                    "district": str,
                    "adcode": str
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'address': address,
                'output': 'json'
            }
            
            if city:
                params['city'] = city
            
            # 发送请求
            url = f"{self.base_url}/geocode/geo"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            geocodes = result.get('geocodes', [])
            if not geocodes:
                return {
                    "status": False,
                    "error": f"未找到地点: {address}"
                }
            
            # 取第一个结果
            geocode = geocodes[0]
            location = geocode.get('location', '').split(',')
            
            if len(location) != 2:
                return {
                    "status": False,
                    "error": "坐标格式错误"
                }
            
            return {
                "status": True,
                "data": {
                    "longitude": float(location[0]),
                    "latitude": float(location[1]),
                    "formatted_address": geocode.get('formatted_address', ''),
                    "province": geocode.get('province', ''),
                    "city": geocode.get('city', ''),
                    "district": geocode.get('district', ''),
                    "adcode": geocode.get('adcode', '')
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def get_city_code(self, city_name: str) -> Dict:
        """
        获取城市的adcode（城市ID）
        
        Args:
            city_name: 城市名称
            
        Returns:
            Dict: 包含城市ID信息的字典
            {
                "status": bool,
                "data": {
                    "city_code": str,
                    "adcode": str,
                    "city_name": str,
                    "province": str,
                    "center": {
                        "longitude": float,
                        "latitude": float
                    }
                },
                "error": str (如果失败)
            }
        """
        try:
            # 构建请求参数
            params = {
                'key': self.api_key,
                'keywords': city_name,
                'subdistrict': '0',  # 不返回下级行政区
                'output': 'json'
            }
            
            # 发送请求
            url = f"{self.base_url}/config/district"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            # 检查API响应状态
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            # 解析结果
            districts = result.get('districts', [])
            if not districts:
                return {
                    "status": False,
                    "error": f"未找到城市: {city_name}"
                }
            
            # 取第一个结果
            district = districts[0]
            center = district.get('center', '').split(',')
            
            center_coords = {}
            if len(center) == 2:
                center_coords = {
                    "longitude": float(center[0]),
                    "latitude": float(center[1])
                }
            
            return {
                "status": True,
                "data": {
                    "city_code": district.get('citycode', ''),
                    "adcode": district.get('adcode', ''),
                    "city_name": district.get('name', ''),
                    "province": district.get('name', ''),
                    "center": center_coords
                }
            }
            
        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"处理错误: {str(e)}"
            }
    
    def batch_geocode(self, addresses: List[str], city: str = None) -> Dict:
        """
        批量地理编码
        
        Args:
            addresses: 地点名称列表
            city: 城市名称，可选
            
        Returns:
            Dict: 批量处理结果
        """
        results = []
        for address in addresses:
            result = self.geocode_address(address, city)
            results.append({
                "address": address,
                "result": result
            })
        
        return {
            "status": True,
            "data": {
                "total": len(addresses),
                "results": results
            }
        }
    
    def search_poi(self, keyword: str, city: str = None, types: str = None) -> Dict:
        """
        POI搜索（兴趣点搜索）
        
        Args:
            keyword: 搜索关键词
            city: 城市名称
            types: POI类型，如"餐饮服务|购物服务"
            
        Returns:
            Dict: POI搜索结果
        """
        try:
            params = {
                'key': self.api_key,
                'keywords': keyword,
                'output': 'json',
                'offset': 20,  # 每页记录数
                'page': 1,     # 当前页数
                'extensions': 'all'  # 返回详细信息
            }
            
            if city:
                params['city'] = city
            if types:
                params['types'] = types
            
            url = f"{self.base_url}/place/text"
            response = requests.get(url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"高德API错误: {result.get('info', '未知错误')}"
                }
            
            pois = result.get('pois', [])
            processed_pois = []
            
            for poi in pois:
                location = poi.get('location', '').split(',')
                poi_data = {
                    "name": poi.get('name', ''),
                    "address": poi.get('address', ''),
                    "type": poi.get('type', ''),
                    "tel": poi.get('tel', ''),
                    "longitude": float(location[0]) if len(location) >= 2 else None,
                    "latitude": float(location[1]) if len(location) >= 2 else None,
                    "adcode": poi.get('adcode', ''),
                    "cityname": poi.get('cityname', '')
                }
                processed_pois.append(poi_data)
            
            return {
                "status": True,
                "data": {
                    "count": result.get('count', 0),
                    "pois": processed_pois
                }
            }
            
        except Exception as e:
            return {
                "status": False,
                "error": f"POI搜索错误: {str(e)}"
            }

    def reverse_geocode_poi(self, longitude: float, latitude: float, radius: int = 1000) -> Dict:
        """
        经纬度转POI（逆地理编码 + 周边搜索）
        输入经纬度，输出附近的POI名称

        Args:
            longitude: 经度
            latitude: 纬度
            radius: 搜索半径（米），默认1000米

        Returns:
            Dict: 包含附近POI信息的字典
            {
                "status": bool,
                "data": {
                    "location": {
                        "longitude": float,
                        "latitude": float
                    },
                    "formatted_address": str,
                    "nearby_pois": [
                        {
                            "name": str,
                            "address": str,
                            "type": str,
                            "distance": int,
                            "longitude": float,
                            "latitude": float
                        }
                    ]
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先进行逆地理编码获取地址信息
            regeo_params = {
                'key': self.api_key,
                'location': f"{longitude},{latitude}",
                'output': 'json',
                'radius': radius,
                'extensions': 'all'
            }

            regeo_url = f"{self.base_url}/geocode/regeo"
            regeo_response = requests.get(regeo_url, params=regeo_params, headers=self.headers, timeout=10)
            regeo_response.raise_for_status()

            regeo_result = regeo_response.json()

            if regeo_result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"逆地理编码错误: {regeo_result.get('info', '未知错误')}"
                }

            # 2. 进行周边POI搜索
            poi_params = {
                'key': self.api_key,
                'location': f"{longitude},{latitude}",
                'radius': radius,
                'output': 'json',
                'offset': 20,
                'page': 1,
                'extensions': 'all'
            }

            poi_url = f"{self.base_url}/place/around"
            poi_response = requests.get(poi_url, params=poi_params, headers=self.headers, timeout=10)
            poi_response.raise_for_status()

            poi_result = poi_response.json()

            if poi_result.get('status') != '1':
                return {
                    "status": False,
                    "error": f"周边POI搜索错误: {poi_result.get('info', '未知错误')}"
                }

            # 处理逆地理编码结果
            regeocode = regeo_result.get('regeocode', {})
            formatted_address = regeocode.get('formatted_address', '')

            # 处理POI结果
            pois = poi_result.get('pois', [])
            nearby_pois = []

            for poi in pois:
                poi_location = poi.get('location', '').split(',')
                poi_data = {
                    "name": poi.get('name', ''),
                    "address": poi.get('address', ''),
                    "type": poi.get('type', ''),
                    "distance": int(poi.get('distance', 0)),
                    "longitude": float(poi_location[0]) if len(poi_location) >= 2 else None,
                    "latitude": float(poi_location[1]) if len(poi_location) >= 2 else None,
                    "tel": poi.get('tel', ''),
                    "business_area": poi.get('business_area', '')
                }
                nearby_pois.append(poi_data)

            return {
                "status": True,
                "data": {
                    "location": {
                        "longitude": longitude,
                        "latitude": latitude
                    },
                    "formatted_address": formatted_address,
                    "nearby_pois": nearby_pois,
                    "total_count": len(nearby_pois)
                }
            }

        except requests.RequestException as e:
            return {
                "status": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            return {
                "status": False,
                "error": f"经纬度转POI错误: {str(e)}"
            }

    def recommend_similar_poi(self, poi_name: str, city: str = None, radius: int = 2000) -> Dict:
        """
        POI推荐 - 根据模糊名称推荐相似POI
        输入一个模糊的POI名称，输出附近相似的POI

        Args:
            poi_name: POI名称，如"北京上地星巴克"
            city: 城市名称，可选
            radius: 搜索半径（米），默认2000米

        Returns:
            Dict: 包含推荐POI信息的字典
            {
                "status": bool,
                "data": {
                    "original_poi": {
                        "name": str,
                        "longitude": float,
                        "latitude": float,
                        "address": str
                    },
                    "similar_pois": [
                        {
                            "name": str,
                            "address": str,
                            "type": str,
                            "distance": int,
                            "longitude": float,
                            "latitude": float,
                            "similarity_score": float
                        }
                    ]
                },
                "error": str (如果失败)
            }
        """
        try:
            # 1. 先搜索原始POI获取其位置
            original_search = self.search_poi(poi_name, city)

            if not original_search.get("status") or not original_search.get("data", {}).get("pois"):
                return {
                    "status": False,
                    "error": f"未找到指定POI: {poi_name}"
                }

            # 取第一个搜索结果作为原始POI
            original_poi = original_search["data"]["pois"][0]
            original_longitude = original_poi["longitude"]
            original_latitude = original_poi["latitude"]

            if original_longitude is None or original_latitude is None:
                return {
                    "status": False,
                    "error": "原始POI坐标信息不完整"
                }

            # 2. 在原始POI周边搜索相似POI
            nearby_result = self.reverse_geocode_poi(original_longitude, original_latitude, radius)

            if not nearby_result.get("status"):
                return {
                    "status": False,
                    "error": f"周边搜索失败: {nearby_result.get('error', '未知错误')}"
                }

            # 3. 分析相似性并推荐
            nearby_pois = nearby_result["data"]["nearby_pois"]

            # 提取原始POI的关键词用于相似性分析
            original_keywords = self._extract_keywords(poi_name)
            original_type = original_poi.get("type", "")

            similar_pois = []
            for poi in nearby_pois:
                # 跳过原始POI本身
                if poi["name"] == original_poi["name"]:
                    continue

                # 计算相似性分数
                similarity_score = self._calculate_similarity(
                    poi, original_keywords, original_type
                )

                if similarity_score > 0.1:  # 相似性阈值
                    poi_with_score = poi.copy()
                    poi_with_score["similarity_score"] = similarity_score
                    similar_pois.append(poi_with_score)

            # 按相似性分数排序
            similar_pois.sort(key=lambda x: x["similarity_score"], reverse=True)

            # 限制返回数量
            similar_pois = similar_pois[:10]

            return {
                "status": True,
                "data": {
                    "original_poi": {
                        "name": original_poi["name"],
                        "longitude": original_longitude,
                        "latitude": original_latitude,
                        "address": original_poi["address"],
                        "type": original_poi["type"]
                    },
                    "similar_pois": similar_pois,
                    "total_count": len(similar_pois),
                    "search_radius": radius
                }
            }

        except Exception as e:
            return {
                "status": False,
                "error": f"POI推荐错误: {str(e)}"
            }

    def _extract_keywords(self, poi_name: str) -> List[str]:
        """从POI名称中提取关键词"""
        # 常见的品牌和类型关键词
        brand_keywords = [
            "星巴克", "麦当劳", "肯德基", "必胜客", "汉堡王", "德克士",
            "华为", "小米", "苹果", "三星", "OPPO", "vivo",
            "万达", "银泰", "大悦城", "万象城", "来福士", "IFS",
            "如家", "汉庭", "7天", "锦江", "华住", "亚朵",
            "中国银行", "工商银行", "建设银行", "农业银行", "招商银行"
        ]

        type_keywords = [
            "咖啡", "餐厅", "酒店", "银行", "商场", "超市", "药店",
            "医院", "学校", "公园", "地铁", "公交", "加油站"
        ]

        keywords = []
        poi_name_lower = poi_name.lower()

        # 提取品牌关键词
        for keyword in brand_keywords:
            if keyword in poi_name:
                keywords.append(keyword)

        # 提取类型关键词
        for keyword in type_keywords:
            if keyword in poi_name:
                keywords.append(keyword)

        return keywords

    def _calculate_similarity(self, poi: Dict, original_keywords: List[str], original_type: str) -> float:
        """计算POI相似性分数"""
        score = 0.0

        poi_name = poi.get("name", "").lower()
        poi_type = poi.get("type", "")

        # 1. 名称关键词匹配 (权重: 0.6)
        keyword_matches = 0
        for keyword in original_keywords:
            if keyword in poi_name:
                keyword_matches += 1

        if original_keywords:
            keyword_score = keyword_matches / len(original_keywords)
            score += keyword_score * 0.6

        # 2. 类型匹配 (权重: 0.3)
        if original_type and poi_type:
            # 简单的类型匹配
            if original_type == poi_type:
                score += 0.3
            elif any(t in poi_type for t in original_type.split('|')):
                score += 0.15

        # 3. 距离因子 (权重: 0.1)
        distance = poi.get("distance", 0)
        if distance > 0:
            # 距离越近分数越高，最大2000米
            distance_score = max(0, (2000 - distance) / 2000)
            score += distance_score * 0.1

        return min(score, 1.0)  # 确保分数不超过1.0


# 使用示例和测试函数
def test_amap_tools():
    """测试高德地图工具"""
    # 需要设置环境变量 AMAP_API_KEY
    try:
        amap = AmapMCPTools()
        
        # 测试地理编码
        print("=== 测试地理编码 ===")
        result1 = amap.geocode_address("西湖", "杭州")
        print(f"西湖坐标: {result1}")
        
        # 测试城市代码获取
        print("\n=== 测试城市代码 ===")
        result2 = amap.get_city_code("杭州")
        print(f"杭州城市代码: {result2}")
        
        # 测试POI搜索
        print("\n=== 测试POI搜索 ===")
        result3 = amap.search_poi("星巴克", "杭州")
        print(f"杭州星巴克: {result3}")

        # 测试经纬度转POI
        print("\n=== 测试经纬度转POI ===")
        result4 = amap.reverse_geocode_poi(116.397428, 39.90923)  # 天安门坐标
        print(f"天安门附近POI: {result4}")

        # 测试POI推荐
        print("\n=== 测试POI推荐 ===")
        result5 = amap.recommend_similar_poi("北京上地星巴克")
        print(f"上地星巴克附近推荐: {result5}")

    except Exception as e:
        print(f"测试失败: {e}")


# MCP工具函数定义，用于集成到现有系统
def mcp_geocode_address(address: str, city: str = None) -> Dict:
    """
    MCP工具：地点转经纬度
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.geocode_address(address, city)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_get_city_code(city_name: str) -> Dict:
    """
    MCP工具：获取城市ID
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.get_city_code(city_name)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_search_poi(keyword: str, city: str = None, types: str = None) -> Dict:
    """
    MCP工具：POI搜索
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.search_poi(keyword, city, types)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_reverse_geocode_poi(longitude: float, latitude: float, radius: int = 1000) -> Dict:
    """
    MCP工具：经纬度转POI
    输入经纬度，输出附近的POI名称
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.reverse_geocode_poi(longitude, latitude, radius)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


def mcp_recommend_similar_poi(poi_name: str, city: str = None, radius: int = 2000) -> Dict:
    """
    MCP工具：POI推荐
    根据模糊名称推荐相似POI
    用于function calling
    """
    try:
        amap = AmapMCPTools()
        return amap.recommend_similar_poi(poi_name, city, radius)
    except Exception as e:
        return {
            "status": False,
            "error": f"初始化高德工具失败: {str(e)}"
        }


# 高德地图工具的function calling定义
AMAP_TOOLS = [
    {
        "type": "function",
        "function": {
            "name": "mcp_geocode_address",
            "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
            "parameters": {
                "type": "object",
                "properties": {
                    "address": {
                        "type": "string",
                        "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                    }
                },
                "required": ["address"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_get_city_code",
            "description": "获取城市的adcode（城市ID）和相关信息。",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {
                        "type": "string",
                        "description": "城市名称，如'杭州'、'北京'、'上海'"
                    }
                },
                "required": ["city_name"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_search_poi",
            "description": "搜索兴趣点（POI），如商店、餐厅、景点等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于限定搜索范围"
                    },
                    "types": {
                        "type": "string",
                        "description": "POI类型，可选，如'餐饮服务'、'购物服务'等"
                    }
                },
                "required": ["keyword"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_reverse_geocode_poi",
            "description": "根据经纬度坐标查找附近的POI（兴趣点）。输入经纬度，输出附近的商店、餐厅、景点等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "longitude": {
                        "type": "number",
                        "description": "经度，如116.397428"
                    },
                    "latitude": {
                        "type": "number",
                        "description": "纬度，如39.90923"
                    },
                    "radius": {
                        "type": "integer",
                        "description": "搜索半径（米），默认1000米，最大3000米"
                    }
                },
                "required": ["longitude", "latitude"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "mcp_recommend_similar_poi",
            "description": "根据POI名称推荐附近相似的POI。例如输入'北京上地星巴克'，会推荐上地星巴克附近的咖啡店，而不是北京所有的星巴克。",
            "parameters": {
                "type": "object",
                "properties": {
                    "poi_name": {
                        "type": "string",
                        "description": "POI名称，可以是模糊名称，如'北京上地星巴克'、'杭州西湖银泰'"
                    },
                    "city": {
                        "type": "string",
                        "description": "城市名称，可选，用于提高搜索精度"
                    },
                    "radius": {
                        "type": "integer",
                        "description": "推荐范围半径（米），默认2000米"
                    }
                },
                "required": ["poi_name"]
            }
        }
    }
]


if __name__ == "__main__":
    test_amap_tools()
