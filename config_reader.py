"""
配置文件读取工具
用于读取和解析config.yaml中的配置信息
"""

import yaml
import json
import os
from typing import Dict, Any, Optional


class ConfigReader:
    """配置文件读取器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置读取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                print(f"❌ 配置文件不存在: {self.config_path}")
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                content = file.read().strip()
                
                # 检查文件格式
                if content.startswith('"main_process_system_prompt"'):
                    # 处理特殊格式的配置文件
                    self.config_data = self._parse_special_format(content)
                else:
                    # 标准YAML格式
                    self.config_data = yaml.safe_load(content)

                # 确保config_data不为None
                if self.config_data is None:
                    self.config_data = {}
                    
            print(f"✅ 成功加载配置文件: {self.config_path}")
            
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            self.config_data = {}
    
    def _parse_special_format(self, content: str) -> Dict[str, Any]:
        """解析特殊格式的配置文件"""
        print(f"原始内容: {content[:100]}...")

        try:
            # 直接手动解析，因为格式比较特殊
            return self._manual_parse(content)

        except Exception as e:
            print(f"解析失败: {str(e)}")
            return {}
    
    def _manual_parse(self, content: str) -> Dict[str, Any]:
        """手动解析配置内容"""
        config = {}

        print(f"开始手动解析，内容长度: {len(content)}")

        # 查找main_process_system_prompt
        if '"main_process_system_prompt"' in content:
            print("找到main_process_system_prompt键")

            # 找到冒号后的内容
            colon_pos = content.find(':', content.find('"main_process_system_prompt"'))
            if colon_pos != -1:
                # 找到第一个单引号
                start_quote = content.find("'", colon_pos)
                if start_quote != -1:
                    # 找到最后一个单引号
                    end_quote = content.rfind("'")
                    if end_quote > start_quote:
                        prompt_content = content[start_quote + 1:end_quote]
                        # 处理转义字符
                        prompt_content = prompt_content.replace('\\n', '\n')
                        config['main_process_system_prompt'] = prompt_content
                        print(f"成功提取，内容长度: {len(prompt_content)}")
                    else:
                        print("未找到结束引号")
                else:
                    print("未找到开始引号")
            else:
                print("未找到冒号")
        else:
            print("未找到main_process_system_prompt键")

        return config
    
    def get_main_process_system_prompt(self) -> Optional[str]:
        """获取主处理系统提示词"""
        return self.config_data.get('main_process_system_prompt')
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self.config_data.get(key, default)
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config_data.copy()
    
    def print_config(self):
        """打印配置信息"""
        print("=== 配置文件内容 ===")
        if not self.config_data:
            print("配置数据为空")
            return

        for key, value in self.config_data.items():
            print(f"\n{key}:")
            if isinstance(value, str) and len(value) > 100:
                # 长文本只显示前100个字符
                print(f"  {value[:100]}...")
                print(f"  (总长度: {len(value)} 字符)")
            else:
                print(f"  {value}")
    
    def save_formatted_config(self, output_path: str = "config_formatted.yaml"):
        """保存格式化的配置文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config_data, file, 
                         default_flow_style=False, 
                         allow_unicode=True,
                         indent=2)
            print(f"✅ 格式化配置已保存到: {output_path}")
        except Exception as e:
            print(f"❌ 保存格式化配置失败: {str(e)}")


def read_main_process_system_prompt(config_path: str = "config.yaml") -> Optional[str]:
    """
    快速读取main_process_system_prompt的便捷函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        系统提示词字符串，如果读取失败返回None
    """
    reader = ConfigReader(config_path)
    return reader.get_main_process_system_prompt()


def main():
    """主函数，演示配置读取功能"""
    print("🔧 配置文件读取工具")
    print("=" * 50)
    
    # 创建配置读取器
    reader = ConfigReader("config.yaml")
    
    # 打印所有配置
    reader.print_config()
    
    # 获取主处理系统提示词
    prompt = reader.get_main_process_system_prompt()
    if prompt:
        print(f"\n=== 主处理系统提示词 ===")
        print(f"长度: {len(prompt)} 字符")
        print(f"内容预览:")
        print(prompt[:200] + "..." if len(prompt) > 200 else prompt)
        
        # 保存格式化版本
        reader.save_formatted_config()
        
        # 保存纯文本版本
        with open("system_prompt.txt", "w", encoding="utf-8") as f:
            f.write(prompt)
        print("✅ 系统提示词已保存到 system_prompt.txt")
        
    else:
        print("❌ 未找到main_process_system_prompt")


if __name__ == "__main__":
    main()
