# 用户行为模拟器 - 快速使用指南

## 🎯 核心功能

✅ **已完成MainAgent集成** - 完全兼容`taxi_agent_system.py`  
✅ **5种用户场景模拟** - 首次使用、有历史、探索、聊天、超出范围  
✅ **智能对话生成** - 基于百炼大模型  
✅ **批量测试分析** - 自动生成测试报告  

## 🚀 快速开始

### 1. 基础测试（无需API密钥）

```bash
# 系统功能测试
python test_user_simulator.py

# 快速演示
python test_user_simulator.py --demo

# MainAgent集成测试
python test_main_agent_integration.py

# 完整功能演示
python final_demo_user_simulator.py
```

### 2. 完整功能（需要API密钥）

```bash
# 设置环境变量
export BAILIAN_API_KEY="your_bailian_api_key"
export AMAP_API_KEY="your_amap_api_key"

# 交互式演示
python demo_user_simulator.py
```

## 📝 核心代码示例

### 基础使用

```python
from user_behavior_simulator import UserBehaviorSimulator

# 初始化（自动集成MainAgent）
simulator = UserBehaviorSimulator()

# 创建会话
session_id = simulator.generate_session_id()
simulator.assign_scenario_to_session(session_id, 1)  # 1=首次使用用户

# 生成用户输入
user_input = simulator.generate_user_input(session_id, "", 1)
print(f"用户: {user_input}")

# 调用MainAgent
response = simulator.taxi_agent.process_user_input(user_input)
print(f"系统: {response}")
```

### 批量测试

```python
# 生成测试场景
test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=2)

# 分析结果
analysis = simulator.analyze_test_results(test_scenarios)
simulator.print_analysis_report(analysis)

# 保存结果
filename = simulator.save_test_results(test_scenarios)
```

## 🎭 用户场景说明

| 场景 | 类型 | 特征 | 示例输入 |
|------|------|------|----------|
| 1 | 首次使用用户 | 信息不完整，需要引导 | "你好，我想打车" |
| 2 | 有历史记录用户 | 熟悉系统，明确需求 | "还是去上次那个地方" |
| 3 | 探索型用户 | 无明确目标，纯探索 | "附近有什么好玩的？" |
| 4 | 随意聊天用户 | 话题跳跃，不着边际 | "今天天气真好" |
| 5 | 超出能力范围用户 | 提出无法处理的需求 | "帮我订酒店" |

## 🔧 MainAgent集成验证

```python
# 验证集成是否成功
from taxi_agent_system import MainAgent
from user_behavior_simulator import UserBehaviorSimulator

# 直接使用MainAgent
agent = MainAgent()
response1 = agent.process_user_input("我要从康德大厦打车到太阳宫")

# 通过模拟器使用MainAgent
simulator = UserBehaviorSimulator()
response2 = simulator.taxi_agent.process_user_input("我要从康德大厦打车到太阳宫")

# 验证类型
print(f"Agent类型: {type(simulator.taxi_agent).__name__}")  # 输出: MainAgent
print(f"响应一致: {response1 == response2}")  # 输出: True
```

## 📊 测试结果格式

### JSON输出示例

```json
{
  "session_id": "uuid",
  "scenario_type": 1,
  "scenario_name": "首次使用用户",
  "conversation": [
    {
      "turn": 1,
      "user_input": "你好，我想打车",
      "system_response": "您好！我可以帮您叫车...",
      "timestamp": "2024-01-01T12:00:00"
    }
  ],
  "summary": {
    "total_turns": 3,
    "is_successful": true
  }
}
```

### 分析报告示例

```
整体统计:
  总场景数: 10
  成功场景数: 8
  成功率: 80.0%
  平均轮次: 3.2

按场景类型统计:
  1. 首次使用用户: 成功率 75.0%
  2. 有历史记录用户: 成功率 90.0%
  ...
```

## 🛠️ 文件说明

| 文件 | 功能 | 用途 |
|------|------|------|
| `user_behavior_simulator.py` | 核心模拟器 | 主要功能实现 |
| `demo_user_simulator.py` | 交互式演示 | 用户友好的演示界面 |
| `test_user_simulator.py` | 系统测试 | 验证功能是否正常 |
| `test_main_agent_integration.py` | 集成测试 | 验证MainAgent集成 |
| `final_demo_user_simulator.py` | 完整演示 | 展示所有功能 |
| `example_with_api_keys.py` | 使用示例 | 编程接口示例 |

## ⚠️ 注意事项

1. **API密钥**: 
   - 未设置`BAILIAN_API_KEY`时使用预设问题
   - 未设置`AMAP_API_KEY`时地图功能受限

2. **成本控制**:
   - 批量测试会产生API调用费用
   - 建议先小规模测试

3. **结果分析**:
   - 成功率基于系统回复关键词判断
   - 可根据实际需求调整判断逻辑

## 🎉 验证成功

运行以下命令验证系统是否正常工作：

```bash
# 1. 基础功能测试
python test_user_simulator.py

# 2. MainAgent集成测试  
python test_main_agent_integration.py

# 3. 完整演示
python final_demo_user_simulator.py
```

如果所有测试都显示"✅ 通过"，说明系统集成成功！

## 📞 支持

- 详细文档: `README_用户行为模拟器.md`
- 项目总结: `用户行为模拟器总结.md`
- 环境配置: `setup_environment.sh`

---

**🚗 用户行为模拟智能体已成功集成MainAgent，可以开始测试taxi_agent_system的智能水平！**
