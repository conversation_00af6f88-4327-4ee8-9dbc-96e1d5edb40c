#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为模拟器最终演示
展示完整的功能，包括MainAgent集成
"""

import os
from user_behavior_simulator import UserBehaviorSimulator
from taxi_agent_system import MainAgent


def demo_main_agent_integration():
    """演示MainAgent集成"""
    print("🚗 用户行为模拟器 - MainAgent集成演示")
    print("=" * 60)
    
    # 检查环境
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  注意: 未设置BAILIAN_API_KEY，将使用预设问题模式")
    else:
        print("✅ BAILIAN_API_KEY 已设置，将使用智能生成模式")
    
    print()
    
    # 1. 直接使用MainAgent
    print("1️⃣  直接使用MainAgent:")
    print("-" * 30)
    
    agent = MainAgent()
    test_input = "我要从康德大厦打车到太阳宫"
    response = agent.process_user_input(test_input)
    
    print(f"输入: {test_input}")
    print(f"输出: {response}")
    print()
    
    # 2. 通过模拟器使用MainAgent
    print("2️⃣  通过模拟器使用MainAgent:")
    print("-" * 30)
    
    simulator = UserBehaviorSimulator()
    
    # 验证集成
    print(f"✅ 模拟器中的agent类型: {type(simulator.taxi_agent).__name__}")
    print()
    
    # 3. 演示5种用户场景
    print("3️⃣  五种用户场景演示:")
    print("-" * 30)
    
    for scenario_type in range(1, 6):
        scenario_name = simulator.user_scenarios[scenario_type]['name']
        print(f"\n【场景 {scenario_type}】{scenario_name}")
        
        # 创建会话
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)
        
        # 生成用户输入
        user_input = simulator.generate_user_input(session_id, "", 1)
        
        # 获取系统回复
        system_response = simulator.taxi_agent.process_user_input(user_input)
        
        print(f"  用户: {user_input}")
        print(f"  系统: {system_response[:80]}..." if len(system_response) > 80 else f"  系统: {system_response}")
    
    print()
    
    # 4. 完整对话演示
    print("4️⃣  完整对话演示:")
    print("-" * 30)
    
    # 选择一个场景进行多轮对话
    session_id = simulator.generate_session_id()
    simulator.assign_scenario_to_session(session_id, 2)  # 有历史记录用户
    
    print("场景: 有历史记录用户")
    print("模拟3轮对话:")
    
    context = ""
    for turn in range(1, 4):
        user_input = simulator.generate_user_input(session_id, context, turn)
        system_response = simulator.taxi_agent.process_user_input(user_input)
        
        print(f"\n第{turn}轮:")
        print(f"  用户: {user_input}")
        print(f"  系统: {system_response[:60]}..." if len(system_response) > 60 else f"  系统: {system_response}")
        
        context = f"用户说：{user_input}\n系统回复：{system_response}"
    
    print()
    
    # 5. 批量测试演示
    print("5️⃣  批量测试演示:")
    print("-" * 30)
    
    print("生成小规模测试场景...")
    test_scenarios = simulator.generate_test_scenarios(num_sessions_per_type=1)
    
    print(f"✅ 生成完成，共 {len(test_scenarios)} 个场景")
    
    # 分析结果
    analysis = simulator.analyze_test_results(test_scenarios)
    
    print("\n📊 测试结果分析:")
    print(f"  总场景数: {analysis['overall_stats']['total_scenarios']}")
    print(f"  成功场景数: {analysis['overall_stats']['successful_scenarios']}")
    print(f"  成功率: {analysis['overall_stats']['success_rate']:.1%}")
    print(f"  平均轮次: {analysis['overall_stats']['average_turns']:.1f}")
    
    # 保存结果
    filename = simulator.save_test_results(test_scenarios)
    print(f"  结果已保存: {filename}")
    
    print()
    
    # 6. 会话管理演示
    print("6️⃣  会话管理演示:")
    print("-" * 30)
    
    # 创建多个会话
    sessions = []
    for i, scenario_type in enumerate([1, 3, 5], 1):
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)
        sessions.append(session_id)
        
        scenario_name = simulator.user_scenarios[scenario_type]['name']
        print(f"  会话{i}: {session_id[:8]}... ({scenario_name})")
    
    print(f"\n✅ 创建了 {len(sessions)} 个会话")
    
    # 查看会话详情
    print("\n会话详情:")
    for i, session_id in enumerate(sessions, 1):
        info = simulator.get_session_info(session_id)
        print(f"  会话{i}: {info['scenario_name']}")
        print(f"    特征: {', '.join(info['characteristics'][:2])}...")
    
    print()
    print("=" * 60)
    print("🎉 演示完成！")
    print()
    print("📝 使用说明:")
    print("1. 设置环境变量 BAILIAN_API_KEY 启用智能生成")
    print("2. 设置环境变量 AMAP_API_KEY 启用地图功能")
    print("3. 运行 python demo_user_simulator.py 进行交互式演示")
    print("4. 运行 python test_user_simulator.py 进行系统测试")


def demo_api_integration():
    """演示API集成（如果有API密钥）"""
    if not os.getenv("BAILIAN_API_KEY"):
        print("💡 提示: 设置BAILIAN_API_KEY后可体验智能生成功能")
        return
    
    print("\n🤖 智能生成演示:")
    print("-" * 30)
    
    simulator = UserBehaviorSimulator()
    
    # 演示智能生成的差异
    print("对比预设问题 vs 智能生成:")
    
    session_id = simulator.generate_session_id()
    simulator.assign_scenario_to_session(session_id, 1)
    
    # 生成多个问题看差异
    for i in range(3):
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"  生成{i+1}: {user_input}")


def main():
    """主函数"""
    try:
        demo_main_agent_integration()
        demo_api_integration()
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
