{"metadata": {"generated_at": "2025-06-21T14:36:05.801931", "total_scenarios": 5, "scenario_types": [1, 2, 3, 4, 5], "generator_version": "1.0"}, "scenarios": [{"session_id": "776172a0-31c1-4d57-b5c8-7a35d3a7295e", "scenario_type": 1, "scenario_name": "首次使用用户", "conversation": [{"turn": 1, "user_input": "这个怎么用啊？", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:36:05.801069", "scenario_type": 1, "scenario_name": "首次使用用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 44}}, {"session_id": "683a5f39-fe06-4e29-838b-bee638091e56", "scenario_type": 2, "scenario_name": "有历史记录用户", "conversation": [{"turn": 1, "user_input": "还是去上次那个地方", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:36:05.801154", "scenario_type": 2, "scenario_name": "有历史记录用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 46}}, {"session_id": "4309c829-66ce-456c-8507-2cd672713a92", "scenario_type": 3, "scenario_name": "探索型用户", "conversation": [{"turn": 1, "user_input": "附近有什么好玩的地方吗？", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:36:05.801234", "scenario_type": 3, "scenario_name": "探索型用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 49}}, {"session_id": "8b5aaaa9-3194-4d74-84bc-f826a38e9145", "scenario_type": 4, "scenario_name": "随意聊天用户", "conversation": [{"turn": 1, "user_input": "我想听个笑话", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:36:05.801309", "scenario_type": 4, "scenario_name": "随意聊天用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 43}}, {"session_id": "2fca629c-3131-4bab-9e42-2d56f7cda016", "scenario_type": 5, "scenario_name": "超出能力范围用户", "conversation": [{"turn": 1, "user_input": "帮我找个餐厅", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:36:05.801378", "scenario_type": 5, "scenario_name": "超出能力范围用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 43}}]}