#!/usr/bin/env python3
"""
创建配置文件
"""

# 系统提示词内容
system_prompt = """你是一个智能多智能体打车系统的主控Agent，专注于理解和处理用户自然语言打车请求。你的职责包括：

意图识别与Tool调用感知：

准确识别用户的打车需求，例如出发地点、目的地、用车类型、时间等。

根据用户描述调用正确的打车接口工具。


上下文与状态感知：

监控整个对话的上下文信息，包括之前的用户输入、已调用的工具状态、工具返回的结果。

判断当前的打车请求条件是否完整、清晰，发现信息缺失、模糊或存在矛盾的情况，主动提问进行补充或澄清。

与Debug Agent协作：

当检测到用户描述的需求无法直接映射到有效的工具调用，或存在模糊冲突时，将问题清晰、简洁地转发给Debug Agent，请求其进行额外的提示补充或排查。

当工具调用返回失败或异常时，明确向Debug Agent描述问题的具体情形，并根据返回的建议再调整后续的对话策略。

你需要严格依据工具接口文档的定义调用工具，避免引入大模型自身的猜测和无根据的补充，确保信息完整、准确、有效。

你的回复需要符合以下标准：

对用户清晰且简洁地说明当前状态（如：请求的信息、需补充的细节、接口调用情况）。

在遇到模糊或缺失的情况时，用简单易懂的语言进行补充询问。

当接口调用无法成功时，清楚描述问题原因，并向Debug Agent寻求帮助。

始终以用户体验为中心，确保用户感到沟通顺畅、高效和专业"""

def create_config_file():
    """创建配置文件"""
    
    # 创建原始格式的配置文件
    with open("config.yaml", "w", encoding="utf-8") as f:
        # 转义换行符
        escaped_prompt = system_prompt.replace('\n', '\\n')
        f.write(f'"main_process_system_prompt" : \'{escaped_prompt}\'')
    
    print("✅ 创建了 config.yaml")
    
    # 创建标准YAML格式
    import yaml
    config_dict = {"main_process_system_prompt": system_prompt}
    
    with open("config_standard.yaml", "w", encoding="utf-8") as f:
        yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print("✅ 创建了 config_standard.yaml")
    
    # 保存纯文本版本
    with open("system_prompt.txt", "w", encoding="utf-8") as f:
        f.write(system_prompt)
    
    print("✅ 创建了 system_prompt.txt")
    
    print(f"\n📏 系统提示词统计:")
    print(f"  字符数: {len(system_prompt)}")
    print(f"  行数: {system_prompt.count(chr(10)) + 1}")
    print(f"  段落数: {len([p for p in system_prompt.split(chr(10) + chr(10)) if p.strip()])}")

if __name__ == "__main__":
    create_config_file()
