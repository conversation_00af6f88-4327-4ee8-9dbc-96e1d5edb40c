# 出租车服务函数模型使用说明

## 概述

本项目将原有的 `langflow_api_V4.py` 改造成了一个可调用的函数模型，主要功能是调用出租车服务。

## 主要改动

### 1. 新增函数模型

添加了 `call_taxi_service()` 函数，这是一个封装好的函数模型，具有以下特点：

- **简化的入参**：只需要提供 `start_place`、`end_place`、`car_prefer` 三个主要参数
- **自动生成模版消息**：根据模版 "用户:打个车从{start_place}出发到{end_place}" 自动生成 `hist_chat` 和 `ori_chat_msg`
- **保持原有功能**：其他参数都保持原有默认值，确保兼容性

### 2. 函数签名

```python
def call_taxi_service(start_place, end_place, car_prefer="", 
                     uid="1923357228652298240", 
                     coords="116.30702, 40.040954",
                     intent_id="10007",
                     req_id="497efdc8-534a-42a1-9211-2d57ebf6df70",
                     conversation_id="1492dac5-c898-4e81-b521-d82606c023c1_1750264990146",
                     app_id="cn.caocaokeji.user",
                     ts="1750264998.105795",
                     scene_id=4,
                     order=4,
                     last_scene_id="-1",
                     last_scene_sub_id="-1",
                     start_alias="",
                     end_alias="",
                     talk="",
                     input="",
                     context=None):
```

## 使用方法

### 基本使用

```python
from langflow_api_V4 import call_taxi_service

# 最简单的调用方式
result = call_taxi_service(
    start_place="康德大厦",
    end_place="机场"
)
```

### 带车辆偏好

```python
result = call_taxi_service(
    start_place="北京站",
    end_place="首都机场",
    car_prefer="舒适型"
)
```

### 自定义更多参数

```python
result = call_taxi_service(
    start_place="三里屯",
    end_place="王府井",
    car_prefer="经济型",
    uid="custom_user_123",
    coords="116.456, 39.928"
)
```

## 返回值

函数返回一个字典，包含以下可能的字段：

### 成功时
- `status`: 0 表示成功
- `action_ret_msg`: 返回的消息内容
- `detail_info`: 详细信息
- `params`: 参数信息
- `input_tokens`: 输入token数量
- `output_tokens`: 输出token数量
- `total_tokens`: 总token数量
- `time_cost`: 执行时间（秒）
- `total_elapsed_time`: 总耗时（秒）

### 失败时
- `status`: 1 表示失败
- `err_code`: 错误代码
- `err_msg`: 错误信息
- `time_cost`: 执行时间（秒）

## 示例代码

查看 `taxi_service_example.py` 文件获取完整的使用示例，包括：

1. 基本使用示例
2. 带车辆偏好的示例
3. 自定义参数示例
4. 批量请求示例
5. 结果分析示例

## 运行示例

```bash
# 运行主文件（包含原始测试和新函数测试）
python langflow_api_V4.py

# 运行示例文件
python taxi_service_example.py
```

## 核心特性

### 1. 模版自动生成
函数会根据输入的 `start_place` 和 `end_place` 自动生成：
- `hist_chat`: "用户:打个车从{start_place}出发到{end_place}"
- `ori_chat_msg`: "打个车从{start_place}出发到{end_place}"

### 2. 错误处理
- 包含完整的异常处理机制
- 返回统一的错误格式
- 记录执行时间

### 3. 兼容性
- 保持原有 LangflowAgent 类的所有功能
- 原有的测试代码仍然可以正常运行
- 新增的函数模型不影响原有逻辑

## 注意事项

1. **API地址和认证**：确保 LangflowAgent 中的 API 地址和认证信息正确
2. **网络连接**：需要能够访问 Langflow API 服务
3. **参数格式**：确保传入的地点名称格式正确
4. **错误处理**：建议在调用时添加适当的错误处理逻辑

## 文件结构

```
├── langflow_api_V4.py          # 主文件（包含原有类和新函数模型）
├── taxi_service_example.py     # 使用示例
└── README_taxi_service.md      # 本说明文件
```

## 技术细节

### 数据流程
1. 接收用户输入的起点、终点和车辆偏好
2. 根据模版生成聊天消息
3. 构建完整的输入数据结构
4. 调用 LangflowAgent 执行任务
5. 等待任务完成并获取结果
6. 清理资源并返回结果

### 性能优化
- 添加了总耗时统计
- 保留了原有的执行时间记录
- 支持异步处理（通过 LangflowAgent 的流式处理）

## 扩展建议

1. **参数验证**：可以添加输入参数的验证逻辑
2. **缓存机制**：对于相同的请求可以考虑添加缓存
3. **重试机制**：对于网络错误可以添加自动重试
4. **日志记录**：添加更详细的日志记录功能
5. **配置文件**：将API地址等配置项移到配置文件中
