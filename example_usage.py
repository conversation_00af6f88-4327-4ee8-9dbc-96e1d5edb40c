#!/usr/bin/env python3
"""
高德地图MCP工具使用示例
演示主要功能的使用方法
"""

import os
import json
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi


def example_1_basic_geocoding():
    """示例1: 基础地理编码"""
    print("=== 示例1: 地点转经纬度 ===")
    
    # 常见地点测试
    places = [
        ("西湖", "杭州"),
        ("天安门广场", "北京"), 
        ("外滩", "上海"),
        ("春熙路", "成都"),
        ("鼓浪屿", "厦门")
    ]
    
    amap = AmapMCPTools()
    
    for place, city in places:
        print(f"\n查询: {place} ({city})")
        result = amap.geocode_address(place, city)
        
        if result["status"]:
            data = result["data"]
            print(f"  ✅ 坐标: {data['longitude']:.6f}, {data['latitude']:.6f}")
            print(f"  📍 地址: {data['formatted_address']}")
            print(f"  🏛️ 行政区: {data['province']} > {data['city']} > {data['district']}")
        else:
            print(f"  ❌ 失败: {result['error']}")


def example_2_city_codes():
    """示例2: 获取城市代码"""
    print("\n=== 示例2: 获取城市ID ===")
    
    cities = ["北京", "上海", "广州", "深圳", "杭州", "成都", "西安", "武汉"]
    
    amap = AmapMCPTools()
    
    for city in cities:
        print(f"\n查询城市: {city}")
        result = amap.get_city_code(city)
        
        if result["status"]:
            data = result["data"]
            print(f"  🏙️ 城市代码: {data['city_code']}")
            print(f"  🆔 行政区代码: {data['adcode']}")
            if data.get('center'):
                center = data['center']
                print(f"  📍 中心坐标: {center['longitude']:.6f}, {center['latitude']:.6f}")
        else:
            print(f"  ❌ 失败: {result['error']}")


def example_3_poi_search():
    """示例3: POI搜索"""
    print("\n=== 示例3: POI搜索 ===")
    
    searches = [
        ("星巴克", "杭州"),
        ("中石化", "北京"),
        ("三甲医院", "上海"),
        ("地铁站", "深圳")
    ]
    
    amap = AmapMCPTools()
    
    for keyword, city in searches:
        print(f"\n搜索: {keyword} (在{city})")
        result = amap.search_poi(keyword, city)
        
        if result["status"]:
            pois = result["data"]["pois"][:3]  # 只显示前3个
            print(f"  📊 找到 {result['data']['count']} 个结果，显示前3个:")
            
            for i, poi in enumerate(pois, 1):
                print(f"    {i}. {poi['name']}")
                print(f"       📍 {poi['address']}")
                if poi['longitude'] and poi['latitude']:
                    print(f"       🗺️ {poi['longitude']:.6f}, {poi['latitude']:.6f}")
                if poi['tel']:
                    print(f"       📞 {poi['tel']}")
        else:
            print(f"  ❌ 失败: {result['error']}")


def example_4_mcp_functions():
    """示例4: MCP函数调用"""
    print("\n=== 示例4: MCP函数调用 ===")
    
    # 这些函数可以直接用于function calling
    print("\n1. MCP地理编码函数:")
    result = mcp_geocode_address("西湖", "杭州")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    print("\n2. MCP城市代码函数:")
    result = mcp_get_city_code("杭州")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    print("\n3. MCP POI搜索函数:")
    result = mcp_search_poi("星巴克", "杭州")
    if result["status"] and result["data"]["pois"]:
        # 只显示第一个结果
        first_poi = result["data"]["pois"][0]
        print("第一个搜索结果:")
        print(json.dumps(first_poi, ensure_ascii=False, indent=2))


def example_5_travel_planning():
    """示例5: 旅行规划场景"""
    print("\n=== 示例5: 旅行规划场景 ===")
    
    print("场景: 规划杭州一日游路线")
    
    # 杭州景点
    attractions = ["西湖", "雷峰塔", "灵隐寺", "宋城", "西溪湿地"]
    
    amap = AmapMCPTools()
    locations = []
    
    print("\n📍 获取各景点坐标:")
    for attraction in attractions:
        result = amap.geocode_address(attraction, "杭州")
        if result["status"]:
            data = result["data"]
            locations.append({
                "name": attraction,
                "longitude": data["longitude"],
                "latitude": data["latitude"],
                "address": data["formatted_address"]
            })
            print(f"  ✅ {attraction}: {data['longitude']:.6f}, {data['latitude']:.6f}")
        else:
            print(f"  ❌ {attraction}: {result['error']}")
    
    # 简单的距离计算
    if len(locations) >= 2:
        print(f"\n📏 计算景点间距离 (直线距离):")
        import math
        
        for i in range(len(locations) - 1):
            loc1 = locations[i]
            loc2 = locations[i + 1]
            
            # 简单的距离计算（公里）
            lat_diff = loc2["latitude"] - loc1["latitude"]
            lon_diff = loc2["longitude"] - loc1["longitude"]
            distance_km = math.sqrt(lat_diff**2 + lon_diff**2) * 111
            
            print(f"  {loc1['name']} → {loc2['name']}: 约 {distance_km:.2f} 公里")


def example_6_batch_processing():
    """示例6: 批量处理"""
    print("\n=== 示例6: 批量处理 ===")
    
    # 批量地理编码
    addresses = [
        "北京大学",
        "清华大学", 
        "中国人民大学",
        "北京师范大学",
        "北京理工大学"
    ]
    
    amap = AmapMCPTools()
    result = amap.batch_geocode(addresses, "北京")
    
    print("批量查询北京高校坐标:")
    for item in result["data"]["results"]:
        address = item["address"]
        geocode_result = item["result"]
        
        if geocode_result["status"]:
            data = geocode_result["data"]
            print(f"  ✅ {address}: {data['longitude']:.6f}, {data['latitude']:.6f}")
        else:
            print(f"  ❌ {address}: {geocode_result['error']}")


def main():
    """主函数"""
    print("🗺️ 高德地图MCP工具使用示例")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv("AMAP_API_KEY"):
        print("❌ 请先设置环境变量 AMAP_API_KEY")
        print("   export AMAP_API_KEY='your_api_key_here'")
        return
    
    try:
        # 运行所有示例
        example_1_basic_geocoding()
        example_2_city_codes()
        example_3_poi_search()
        example_4_mcp_functions()
        example_5_travel_planning()
        example_6_batch_processing()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 运行示例时出错: {str(e)}")
        print("请检查网络连接和API密钥设置")


if __name__ == "__main__":
    main()
