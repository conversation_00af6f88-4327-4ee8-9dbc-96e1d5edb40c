#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版打车Agent测试文件
测试集成了高德地图工具和打车服务的增强版Agent
"""

import json
import time
from taxi_agent_system import EnhancedTaxiAgent
from langflow_api_V4 import call_taxi_service


def test_call_taxi_service_direct():
    """直接测试 call_taxi_service 函数"""
    print("=== 直接测试 call_taxi_service 函数 ===")
    
    try:
        result = call_taxi_service(
            start_place="康德大厦",
            end_place="太阳",
            car_prefer=""
        )
        
        print("原始输出格式:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None


def test_format_taxi_result():
    """测试打车服务结果格式化"""
    print("\n=== 测试打车服务结果格式化 ===")
    
    # 模拟原始结果
    mock_result = {
        'action_ret_msg': '康得大厦到太阳宫(地铁站)，可以么？',
        'status': 1,
        'err_msg': '',
        'err_code': 'success',
        'detail_info': {},
        'params': {
            'output': '康得大厦到太阳宫(地铁站)，可以么？',
            'pickup_name': '康得大厦',
            'pickup_l': '116.304228',
            'pickup_r': '40.042815',
            'dest_name': '太阳宫(地铁站)',
            'dest_l': '116.448213',
            'dest_r': '39.973816',
            'sub_id': 2
        },
        'time_cost': 0.8740000000000001
    }
    
    agent = EnhancedTaxiAgent()
    formatted_result = agent._format_taxi_service_result(mock_result)
    
    print("格式化后的结果:")
    print(json.dumps(formatted_result, indent=2, ensure_ascii=False))
    
    return formatted_result


def test_amap_tools():
    """测试高德地图工具"""
    print("\n=== 测试高德地图工具 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试地理编码
    print("1. 测试地理编码:")
    try:
        result = agent._execute_function(
            "mcp_geocode_address",
            {"address": "康德大厦", "city": "北京"},
            "test_session"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"地理编码测试失败: {e}")
    
    # 测试城市代码
    print("\n2. 测试城市代码:")
    try:
        result = agent._execute_function(
            "mcp_get_city_code",
            {"city_name": "北京"},
            "test_session"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"城市代码测试失败: {e}")
    
    # 测试POI搜索
    print("\n3. 测试POI搜索:")
    try:
        result = agent._execute_function(
            "mcp_search_poi",
            {"keyword": "星巴克", "city": "北京"},
            "test_session"
        )
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"POI搜索测试失败: {e}")


def test_integrated_taxi_service():
    """测试集成的打车服务"""
    print("\n=== 测试集成的打车服务 ===")
    
    agent = EnhancedTaxiAgent()
    
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "test_session"
        )
        
        print("集成后的打车服务结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return result
        
    except Exception as e:
        print(f"集成打车服务测试失败: {e}")
        return None


def test_conversation_flow():
    """测试完整的对话流程"""
    print("\n=== 测试完整的对话流程 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 模拟对话
    conversations = [
        "你好，我想了解一下康德大厦的位置",
        "我要从康德大厦打车到太阳宫",
        "帮我查一下北京有哪些星巴克"
    ]
    
    session_id = "test_conversation"
    
    for i, message in enumerate(conversations, 1):
        print(f"\n--- 对话 {i} ---")
        print(f"用户: {message}")
        
        try:
            response = agent.process_message(message, session_id)
            print(f"助手: {response}")
        except Exception as e:
            print(f"对话处理失败: {e}")
        
        # 添加延迟避免API限制
        time.sleep(1)


def test_function_calling_tools():
    """测试Function Calling工具定义"""
    print("\n=== 测试Function Calling工具定义 ===")
    
    agent = EnhancedTaxiAgent()
    
    print("可用工具:")
    for i, tool in enumerate(agent.tools, 1):
        print(f"{i}. {tool['function']['name']}")
        print(f"   描述: {tool['function']['description']}")
        print(f"   必需参数: {tool['function']['parameters'].get('required', [])}")
        print()


def main():
    """主测试函数"""
    print("增强版打车Agent测试")
    print("=" * 60)
    
    # 1. 测试Function Calling工具定义
    test_function_calling_tools()
    
    # 2. 测试高德地图工具
    test_amap_tools()
    
    # 3. 直接测试打车服务
    test_call_taxi_service_direct()
    
    # 4. 测试结果格式化
    test_format_taxi_result()
    
    # 5. 测试集成的打车服务
    test_integrated_taxi_service()
    
    # 6. 测试完整对话流程
    test_conversation_flow()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
