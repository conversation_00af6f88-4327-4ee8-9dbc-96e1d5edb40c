from typing import Dict, List, Optional
from datetime import datetime
import json
import requests
import os
from openai import OpenAI
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi, mcp_reverse_geocode_poi, mcp_recommend_similar_poi, AMAP_TOOLS
from langflow_api_V4 import call_taxi_service
import time

class StateManager:
    """全局状态管理器"""
    def __init__(self):
        self.conversation_state = {}
        self.api_results = {}
        self.missing_params = {}
        self.execution_history = []
        self.current_status = {
            'last_action': None,
            'status': 'ready',  # ready, processing, success, failed
            'error': None
        }
        
    def log_execution(self, action: str, status: str, details: Dict):
        """记录执行历史"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.execution_history.append(entry)
        self.current_status = {
            'last_action': action,
            'status': status,
            'error': details.get('error')
        }
        
    def update_state(self, key: str, value: any):
        self.conversation_state[key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_api_call(self, api_name: str, result: Dict):
        self.api_results[api_name] = result
        
    def track_missing_param(self, param: str, question: str):
        self.missing_params[param] = question


class ToolAgent:
    """集成百炼function calling的工具调用Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        # self.client = BailianClient()
        
    async def call_api(self, endpoint: str, params: Dict) -> Dict:
        try:
            # 记录开始状态
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="processing",
                details={"params": params}
            )
            
            # 调用百炼function calling
            func_call = {
                "name": endpoint,
                "parameters": params
            }
            response = await self.client.function_calling(func_call)
            
            # 处理响应
            if response.status == "success":
                result = {
                    "status": True,
                    "data": response.data
                }
                self.state.log_api_call(endpoint, result)
                self.state.log_execution(
                    action=f"call_api:{endpoint}",
                    status="success",
                    details={"response": result}
                )
                return result
            else:
                # TODO: 要增加 执行错误的日志记录
                raise Exception(response.error)
                
        except Exception as e:
            error_result = {
                "status": False,
                "error": str(e)
            }
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

class CompletionAgent:
    """条件补全Agent""" 
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        
    def generate_clarification(self, missing: Dict) -> str:
        questions = []
        for param, question in missing.items():
            questions.append(f"{question} (需要{param})")
        return "请提供以下信息:\n" + "\n".join(questions)

class DebugAgent:
    """生活助手Debug Agent - 支持5种用户场景"""
    def __init__(self, state_manager: StateManager, bailian_client=None):
        self.state = state_manager
        self.bailian_client = bailian_client
        self.error_log = []
        self.user_scenarios = {
            "new_user": "首次使用信息不全",
            "returning_user": "有明确历史记录", 
            "exploring_user": "无明确目标探索",
            "random_user": "不着边际的请求",
            "out_of_scope": "超出能力范围"
        }
        self.function_debug_log = []

    def step1_pre_validation(self, function_name: str, args: dict, user_intent: str, session_id: str = "default") -> dict:
        """步骤1: 增强的前置验证，支持用户场景分析"""
        validation_result = {
            "is_valid": True,
            "missing_params": [],
            "error_code": 1,  # 1: 完全成功, 0.5: 成功但有警告, 0: 缺少参数, -1: 函数解析错误
            "issues": [],
            "user_scenario": None
        }

        # 检测用户场景
        user_scenario = self._detect_user_scenario(session_id, user_intent)
        validation_result["user_scenario"] = user_scenario

        # 检查函数是否存在
        available_functions = ["mcp_geocode_address", "mcp_get_city_code", "mcp_search_poi", "mcp_reverse_geocode_poi", "mcp_recommend_similar_poi", "call_taxi_service"]
        if function_name not in available_functions:
            validation_result.update({
                "is_valid": False,
                "error_code": -1,
                "issues": [f"未知函数: {function_name}"]
            })
            return validation_result

        # 检查必需参数
        required_params = self._get_required_params(function_name)
        missing = []

        for param in required_params:
            if param not in args or not args[param] or str(args[param]).strip() == "":
                missing.append(param)

        if missing:
            validation_result.update({
                "is_valid": False,
                "missing_params": missing,
                "error_code": 0,
                "issues": [f"缺少必需参数: {', '.join(missing)}"]
            })

        # 检查参数合理性
        reasonableness_check = self._check_parameter_reasonableness(function_name, args, user_intent)
        if not reasonableness_check["is_reasonable"]:
            validation_result.update({
                "is_valid": False,
                "error_code": 0,
                "issues": reasonableness_check["issues"]
            })

        # 根据用户场景调整验证严格程度
        if user_scenario == "new_user" and missing:
            validation_result["error_code"] = 0.5  # 对新用户更宽容
            validation_result["issues"].append("新用户可能需要更多引导")

        return validation_result

    def step2_parameter_completion(self, function_name: str, missing_params: list, user_intent: str, session_id: str) -> dict:
        """步骤2: 参数补全 - 根据用户场景生成澄清问题"""
        if not self.bailian_client or not missing_params:
            return {"clarification_needed": False, "question": ""}

        try:
            # 获取用户场景
            user_scenario = self._detect_user_scenario(session_id, user_intent)
            
            # 根据场景定制提示词
            scenario_prompts = {
                "new_user": "这是用户第一次使用系统，请用更详细的解释和示例",
                "returning_user": "用户有使用经验，可以直接询问",
                "exploring_user": "用户可能在探索功能，提供更多引导",
                "random_user": "用户可能不清楚需求，需要明确询问",
                "out_of_scope": "用户请求可能超出范围，需要友好解释"
            }
            
            scenario_instruction = scenario_prompts.get(user_scenario, "")

            # 生成针对性的澄清问题
            prompt = f"""用户想要使用{function_name}功能，但缺少以下参数：{', '.join(missing_params)}
用户原始意图：{user_intent}
用户场景：{user_scenario} - {scenario_instruction}

请生成一个简洁的澄清问题来获取缺失信息。要求：
1. 问题要简短明了，适合语音输出
2. 一次只问最关键的1-2个参数
3. 根据用户场景调整语气和详细程度
4. 提供具体的例子帮助用户理解

只返回问题内容，不要其他解释。"""

            response = self.bailian_client.chat.completions.create(
                model="qwen-max",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=100,
                temperature=0.3
            )

            question = response.choices[0].message.content.strip()

            return {
                "clarification_needed": True,
                "question": question,
                "missing_params": missing_params,
                "user_scenario": user_scenario
            }

        except Exception as e:
            # 回退到预设问题
            return {
                **self._get_fallback_clarification(function_name, missing_params),
                "user_scenario": "default"
            }

    def step3_execute_function(self, function_name: str, args: dict) -> dict:
        """步骤3: 执行函数调用"""
        try:
            # 这里会调用实际的函数执行逻辑
            # 在主类中实现具体的函数调用
            return {"status": "ready_to_execute", "function_name": function_name, "args": args}
        except Exception as e:
            return {"status": "execution_failed", "error": str(e)}

    def step4_result_analysis(self, function_name: str, args: dict, result: dict, user_intent: str, session_id: str = "default") -> dict:
        """步骤4: 结果分析和候选项提供 - 根据用户场景优化"""
        analysis = {
            "success": result.get("status", False),
            "candidates": [],
            "suggestions": [],
            "need_clarification": False,
            "user_scenario": None
        }

        # 获取用户场景
        user_scenario = self._detect_user_scenario(session_id, user_intent)
        analysis["user_scenario"] = user_scenario

        if not result.get("status", False):
            # 执行失败，根据用户场景提供错误分析和建议
            analysis["suggestions"] = self._analyze_failure_and_suggest(function_name, args, result, user_intent)
            
            # 根据用户场景调整建议
            if user_scenario == "new_user":
                analysis["suggestions"].append("作为新用户，您可以尝试更简单的查询")
            elif user_scenario == "returning_user":
                analysis["suggestions"].append("您可以参考之前的成功查询")
            elif user_scenario == "exploring_user":
                analysis["suggestions"].append("您可以尝试不同的搜索关键词")
                
            return analysis

        # 成功执行，分析结果并提供候选项
        if function_name == "mcp_search_poi":
            analysis["candidates"] = self._extract_poi_candidates(result)
            # 根据用户场景调整候选项数量
            if user_scenario == "new_user":
                analysis["candidates"] = analysis["candidates"][:3]  # 新用户只显示前3个
        elif function_name == "mcp_reverse_geocode_poi":
            analysis["candidates"] = self._extract_nearby_poi_candidates(result)
            analysis["suggestions"] = self._analyze_reverse_geocode_result(result, user_intent)
        elif function_name == "mcp_recommend_similar_poi":
            analysis["candidates"] = self._extract_similar_poi_candidates(result)
            analysis["suggestions"] = self._analyze_poi_recommendation_result(result, user_intent)
        elif function_name == "call_taxi_service":
            analysis["suggestions"] = self._analyze_taxi_result(result, user_intent)
        elif function_name == "mcp_geocode_address":
            analysis["suggestions"] = self._analyze_location_result(result, user_intent)

        # 常识性检查
        reasonableness = self._check_result_reasonableness(function_name, args, result, user_intent)
        if not reasonableness["is_reasonable"]:
            analysis["need_clarification"] = True
            analysis["suggestions"].extend(reasonableness["suggestions"])

            # 根据用户场景调整澄清方式
            if user_scenario == "new_user":
                analysis["suggestions"].append("作为新用户，您可以提供更具体的位置信息")
            elif user_scenario == "random_user":
                analysis["suggestions"].append("请确认您的查询意图")

        return analysis

    def _get_required_params(self, function_name: str) -> list:
        """获取函数的必需参数"""
        param_map = {
            "mcp_geocode_address": ["address"],
            "mcp_get_city_code": ["city_name"],
            "mcp_search_poi": ["keyword"],
            "mcp_reverse_geocode_poi": ["longitude", "latitude"],
            "mcp_recommend_similar_poi": ["poi_name"],
            "call_taxi_service": ["start_place", "end_place"]
        }
        return param_map.get(function_name, [])

    def _detect_user_scenario(self, session_id: str, user_intent: str) -> str:
        """识别用户场景类型"""
        # 简单实现 - 实际应根据会话历史更智能判断
        if "session_history" not in self.state.conversation_state.get(session_id, {}):
            return "new_user"
        return "default"
        """分类错误类型"""
        error_lower = error.lower()

        if any(keyword in error_lower for keyword in ['api_key', 'authentication', 'unauthorized']):
            return "API_AUTH_ERROR"
        elif any(keyword in error_lower for keyword in ['network', 'connection', 'timeout']):
            return "NETWORK_ERROR"
        elif any(keyword in error_lower for keyword in ['rate limit', 'quota', 'exceeded']):
            return "RATE_LIMIT_ERROR"
        elif any(keyword in error_lower for keyword in ['function', 'parameter', 'argument']):
            return "FUNCTION_ERROR"
        elif any(keyword in error_lower for keyword in ['json', 'parse', 'format']):
            return "DATA_FORMAT_ERROR"
        else:
            return "UNKNOWN_ERROR"

    def get_diagnosis(self) -> str:
        """获取系统诊断"""
        if not self.error_log:
            return "系统运行正常，无错误记录"

        # 分析最近的错误
        recent_errors = self.error_log[-5:]  # 最近5个错误
        error_types = {}

        for error in recent_errors:
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        # 找出最常见的错误类型
        most_common_error = max(error_types.items(), key=lambda x: x[1]) if error_types else None

        diagnosis = f"最近错误: {self.error_log[-1]['error']}\n"

        if most_common_error:
            error_type, count = most_common_error
            diagnosis += f"主要错误类型: {error_type} (出现{count}次)\n"
            diagnosis += self._get_error_suggestions(error_type)

        return diagnosis

    def _get_error_suggestions(self, error_type: str) -> str:
        """根据错误类型提供建议"""
        suggestions = {
            "API_AUTH_ERROR": "建议检查API密钥配置和权限设置",
            "NETWORK_ERROR": "建议检查网络连接和防火墙设置",
            "RATE_LIMIT_ERROR": "建议降低请求频率或升级API套餐",
            "FUNCTION_ERROR": "建议检查函数参数格式和必需参数",
            "DATA_FORMAT_ERROR": "建议检查数据格式和JSON解析",
            "UNKNOWN_ERROR": "建议查看详细错误日志并联系技术支持"
        }
        return suggestions.get(error_type, "建议查看详细错误信息")

    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total = self.performance_metrics["successful_requests"] + self.performance_metrics["failed_requests"]
        success_rate = (self.performance_metrics["successful_requests"] / total * 100) if total > 0 else 0

        return {
            "total_requests": total,
            "success_rate": f"{success_rate:.1f}%",
            "successful_requests": self.performance_metrics["successful_requests"],
            "failed_requests": self.performance_metrics["failed_requests"],
            "average_response_time": f"{self.performance_metrics['average_response_time']:.2f}s",
            "recent_errors": len([e for e in self.error_log if
                                (datetime.now() - datetime.fromisoformat(e['timestamp'])).seconds < 300])
        }

    def get_fix_suggestions(self) -> List[str]:
        """获取修正建议"""
        if not self.error_log:
            return ["系统运行正常，无需修正"]

        suggestions = []

        # 分析错误模式
        error_types = {}
        for error in self.error_log[-10:]:  # 最近10个错误
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        # 根据错误类型提供具体建议
        for error_type, count in error_types.items():
            if count >= 3:  # 如果同类错误出现3次以上
                if error_type == "API_AUTH_ERROR":
                    suggestions.extend([
                        "检查环境变量 AMAP_API_KEY 和 BAILIAN_API_KEY 是否正确设置",
                        "验证API密钥是否有效且未过期",
                        "确认API服务权限配置正确"
                    ])
                elif error_type == "NETWORK_ERROR":
                    suggestions.extend([
                        "检查网络连接状态",
                        "验证防火墙和代理设置",
                        "考虑增加请求超时时间"
                    ])
                elif error_type == "RATE_LIMIT_ERROR":
                    suggestions.extend([
                        "实现请求频率限制",
                        "添加请求重试机制",
                        "考虑升级API套餐"
                    ])
                elif error_type == "FUNCTION_ERROR":
                    suggestions.extend([
                        "检查函数参数格式和类型",
                        "验证必需参数是否完整",
                        "确认函数定义正确"
                    ])

        # 检查重复错误
        recent_error_messages = [e['error'] for e in self.error_log[-5:]]
        if len(set(recent_error_messages)) == 1 and len(recent_error_messages) > 2:
            suggestions.append("检测到重复错误，建议检查根本原因")

        return suggestions if suggestions else ["查看详细错误日志以获取更多信息"]

    def log_function_debug(self, debug_info: dict):
        """记录Function调试信息"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            **debug_info
        }
        self.function_debug_log.append(entry)

        # 保持日志大小合理
        if len(self.function_debug_log) > 100:
            self.function_debug_log = self.function_debug_log[-50:]

    def log_error(self,error_info, debug_info: dict):
        print(error_info)
        debug_info["error_info"]=error_info
        self.function_debug_log.append(debug_info)


    def get_function_debug_summary(self) -> dict:
        """获取Function调试摘要"""
        if not self.function_debug_log:
            return {"message": "暂无Function调试记录"}

        # 统计各种调试分数
        scores = [entry.get("debug_score", 1.0) for entry in self.function_debug_log]

        score_distribution = {
            "perfect": len([s for s in scores if s == 1.0]),
            "partial_success": len([s for s in scores if s == 0.5]),
            "parameter_issues": len([s for s in scores if s == 0]),
            "function_errors": len([s for s in scores if s == -1])
        }

        # 最近的问题
        recent_issues = []
        for entry in self.function_debug_log[-10:]:
            if entry.get("issues"):
                recent_issues.extend(entry["issues"])

        return {
            "total_function_calls": len(self.function_debug_log),
            "score_distribution": score_distribution,
            "recent_issues": recent_issues[-5:],  # 最近5个问题
            "success_rate": f"{(score_distribution['perfect'] / len(scores) * 100):.1f}%" if scores else "0%"
        }

class EnhancedTaxiAgent:
    """增强版打车Agent，集成高德地图和打车服务"""
    def __init__(self):
        self.state = StateManager()
        self.tool_agent = ToolAgent(self.state)
        self.completion_agent = CompletionAgent(self.state)
        self.debug_agent = DebugAgent(self.state)

        # 百炼模型配置
        api_key = os.getenv("BAILIAN_API_KEY")
        if api_key:
            self.bailian_client = OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            self.bailian_model_name = "qwen-max"
        else:
            print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能将不可用")
            self.bailian_client = None
            self.bailian_model_name = None

        # 初始化高德地图工具
        try:
            self.amap_tools = AmapMCPTools()
        except Exception as e:
            print(f"警告: 高德地图工具初始化失败: {e}")
            self.amap_tools = None

        # 系统提示词
        self.system_prompt = """你是一个智能助手，可以调用工具帮助用户完成任务。如果工具执行错误或者没有可选工具，需要引导用户补充信息、通过联网搜索满足、判断事情和合法性
请根据用户的需求选择合适的工具来帮助用户。不过需要注意的每轮最输出15个字以内。"""

        self.context = {}  # 存储对话上下文

        # 定义可用的工具
        self.tools = [
            # 高德地图工具
            {
                "type": "function",
                "function": {
                    "name": "mcp_geocode_address",
                    "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "address": {
                                "type": "string",
                                "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                            }
                        },
                        "required": ["address"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_get_city_code",
                    "description": "获取城市的adcode（城市ID）和相关信息。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city_name": {
                                "type": "string",
                                "description": "城市名称，如'杭州'、'北京'、'上海'"
                            }
                        },
                        "required": ["city_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_search_poi",
                    "description": "搜索POI（兴趣点），如餐厅、酒店、景点等。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "keyword": {
                                "type": "string",
                                "description": "搜索关键词，如'星巴克'、'酒店'、'加油站'"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于限定搜索范围"
                            },
                            "types": {
                                "type": "string",
                                "description": "POI类型，可选，如'餐饮服务'、'住宿服务'"
                            }
                        },
                        "required": ["keyword"]
                    }
                }
            },
            # 打车服务工具
            {
                "type": "function",
                "function": {
                    "name": "call_taxi_service",
                    "description": "调用打车服务，为用户安排车辆从起点到终点。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "start_place": {
                                "type": "string",
                                "description": "出发地点名称，必须提供"
                            },
                            "end_place": {
                                "type": "string",
                                "description": "目的地名称，必须提供"
                            },
                            "car_prefer": {
                                "type": "string",
                                "description": "车辆偏好，可选，如'经济型'、'舒适型'、'豪华型'等"
                            }
                        },
                        "required": ["start_place", "end_place"]
                    }
                }
            }
        ]

        self.tools_extend_feature = {"call_taxi_service":
            {"name": "call_taxi_service",
             "type":"function",
             "backup_format_description":"打车从北京大学到海淀区方正大厦，经济型",
             "low_fault_tolerance":"低"
             },"mcp_geocode_address":
            {"name": "mcp_geocode_address",
             "type":"function",
             "backup_format_description":"杭州西湖的经纬度坐标",
             "low_fault_tolerance":"高"
             },
             "mcp_get_city_code":{"name": "mcp_get_city_code",
              "type":"function",
              "backup_format_description":"杭州市的adcode",
              "low_fault_tolerance":"高"
              },
             "mcp_search_poi":{"name": "mcp_search_poi",
              "type":"function",
              "backup_format_description":"海淀区的星巴克",
              "low_fault_tolerance":"高"
              }      
        }

    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息的主要方法"""
        try:
            # 记录开始状态
            self.state.log_execution(
                action="process_message",
                status="processing",
                details={"user_input": user_input, "session_id": session_id}
            )

            # 检查是否有可用的AI客户端
            if not self.bailian_client:
                return "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。"

            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]

            # 添加用户消息
            self.context[session_id].append({"role": "user", "content": user_input})

            # 调用百炼API获取响应
            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                tool_choice="auto"
            )

            response_message = response.choices[0].message
            self.context[session_id].append(response_message)

            # 检查是否需要function calling
            if response_message.tool_calls:
                # 处理每个function call
                for tool_call in response_message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)

                    # Function执行前的调试分析
                    debug_result = self._debug_function_call_pre(function_name, function_args, session_id)

                    # 根据调试结果决定是否执行
                    if debug_result["should_execute"]:
                        # 执行function
                        function_response = self._execute_function(
                            function_name,
                            function_args,
                            session_id
                        )

                        # Function执行后的调试分析
                        self._debug_function_call_post(function_name, function_args, function_response, session_id)
                    else:
                        # 使用调试建议作为响应
                        function_response = debug_result["fallback_response"]
                    print("function_response:",function_response)
                    # 将结果添加到上下文
                    fault_tolerance_id = self.tools_extend_feature(function_name)["low_fault_tolerance"]
                    actionable_info = f"\n 该函数的容错性是{fault_tolerance_id}"
                    print("actionable_info:",actionable_info)
                    self.context[session_id].append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": function_name,
                        "content": json.dumps(function_response, ensure_ascii=False)+actionable_info
                    })

                # 获取最终回复并优化简洁性
                final_response = self.bailian_client.chat.completions.create(
                    model=self.bailian_model_name,
                    messages=self.context[session_id] + [
                        {"role": "system", "content": "请保持回复简洁明了，言简意赅，不要重复信息。回复用户问题，如果工具的容错性较低,可以直接执行，如果工具的容错性较高，需要询问用户进行校验，比如打车、点咖啡等花钱的任务。"}
                    ]
                )

                final_message = final_response.choices[0].message.content

                # 进一步优化回复简洁性
                final_message = self._optimize_response_brevity(final_message)

                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message
            else:
                # 直接返回响应
                final_message = response_message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            self.debug_agent.log_error(str(e), {"input": user_input, "session_id": session_id})
            self.state.log_execution(
                action="process_message",
                status="failed",
                details={"error": str(e)}
            )
            return error_msg

    def _execute_function(self, function_name: str, args: dict, session_id: str) -> dict:
        """执行function并处理结果"""
        try:
            # 记录function调用
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="processing",
                details={"args": args}
            )

            # 根据function name调用不同的工具
            if function_name == "mcp_geocode_address":
                result = mcp_geocode_address(
                    address=args["address"],
                    city=args.get("city")
                )
            elif function_name == "mcp_get_city_code":
                result = mcp_get_city_code(
                    city_name=args["city_name"]
                )
            elif function_name == "mcp_search_poi":
                result = mcp_search_poi(
                    keyword=args["keyword"],
                    city=args.get("city"),
                    types=args.get("types")
                )
            elif function_name == "mcp_reverse_geocode_poi":
                result = mcp_reverse_geocode_poi(
                    longitude=args["longitude"],
                    latitude=args["latitude"],
                    radius=args.get("radius", 1000)
                )
            elif function_name == "mcp_recommend_similar_poi":
                result = mcp_recommend_similar_poi(
                    poi_name=args["poi_name"],
                    city=args.get("city"),
                    radius=args.get("radius", 2000)
                )
            elif function_name == "call_taxi_service":
                # 调用打车服务并统一输出格式
                raw_result = call_taxi_service(
                    start_place=args["start_place"],
                    end_place=args["end_place"],
                    car_prefer=args.get("car_prefer", "")
                )
                # 统一格式化输出
                result = self._format_taxi_service_result(raw_result)
            else:
                raise ValueError(f"未知的工具: {function_name}")

            # 记录成功状态
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="success",
                details={"result": result}
            )

            return result

        except Exception as e:
            error_result = {"status": False, "error": str(e)}
            self.debug_agent.log_error(str(e), {
                "function": function_name,
                "args": args
            })
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

    def _handle_ambiguous_location(self, location: str, city: str = None) -> dict:
        """处理模糊地点，自动使用POI搜索获取推荐"""
        try:
            # 检测是否为模糊地点
            ambiguous_keywords = ["机场", "大悦城", "万达", "银泰", "商场", "医院", "火车站", "地铁站", "公园"]

            if any(keyword in location for keyword in ambiguous_keywords):
                print(f"检测到模糊地点：{location}，正在搜索具体位置...")

                # 使用POI搜索获取具体位置
                poi_result = self._execute_function(
                    "mcp_search_poi",
                    {"keyword": location, "city": city or "北京"},
                    "ambiguous_location_session"
                )

                if poi_result.get("status") and poi_result.get("data", {}).get("pois"):
                    pois = poi_result["data"]["pois"][:5]  # 取前5个结果

                    recommendations = []
                    for i, poi in enumerate(pois, 1):
                        recommendations.append({
                            "index": i,
                            "name": poi.get("name", ""),
                            "address": poi.get("address", ""),
                            "location": poi.get("location", "")
                        })

                    return {
                        "status": True,
                        "is_ambiguous": True,
                        "original_location": location,
                        "recommendations": recommendations,
                        "message": f"找到{len(recommendations)}个相关地点，请选择具体位置"
                    }
                else:
                    return {
                        "status": False,
                        "is_ambiguous": True,
                        "original_location": location,
                        "message": f"未找到与'{location}'相关的具体地点"
                    }
            else:
                return {
                    "status": True,
                    "is_ambiguous": False,
                    "original_location": location,
                    "message": "地点描述具体，无需进一步搜索"
                }

        except Exception as e:
            return {
                "status": False,
                "is_ambiguous": True,
                "original_location": location,
                "error": str(e),
                "message": f"处理模糊地点时出错：{str(e)}"
            }

    def _debug_function_call_pre(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """Function执行前的调试分析"""
        debug_score = 1.0  # 默认正常
        issues = []
        should_execute = True
        fallback_response = None

        # 1. 功能解析错误检查 (score: -1)
        available_functions = [tool["function"]["name"] for tool in self.tools]
        if function_name not in available_functions:
            debug_score = -1
            issues.append(f"功能解析错误：未知功能 '{function_name}'")
            should_execute = False
            fallback_response = {
                "status": False,
                "error": f"抱歉，我不支持 '{function_name}' 功能。",
                "available_functions": available_functions
            }

        # 2. 参数缺失检查 (score: 0)
        elif self._check_missing_parameters(function_name, function_args):
            debug_score = 0
            missing_params = self._get_missing_parameters(function_name, function_args)
            issues.append(f"参数缺失：{missing_params}")
            should_execute = False
            fallback_response = {
                "status": False,
                "error": f"请提供必需参数：{', '.join(missing_params)}",
                "missing_parameters": missing_params
            }

        # 3. 参数合理性检查 (score: 0.5)
        elif self._check_parameter_validity(function_name, function_args):
            debug_score = 0.5
            validity_issues = self._get_parameter_validity_issues(function_name, function_args)
            issues.append(f"参数不合理：{validity_issues}")

            # 特殊处理：如果是模糊地点，尝试POI搜索
            if function_name == "call_taxi_service" and "模糊" in validity_issues:
                start_place = function_args.get("start_place", "")
                end_place = function_args.get("end_place", "")

                # 处理起点模糊地点
                if start_place:
                    start_result = self._handle_ambiguous_location(start_place)
                    if start_result.get("is_ambiguous") and start_result.get("recommendations"):
                        issues.append(f"起点'{start_place}'有多个选择，建议明确具体位置")

                # 处理终点模糊地点
                if end_place:
                    end_result = self._handle_ambiguous_location(end_place)
                    if end_result.get("is_ambiguous") and end_result.get("recommendations"):
                        issues.append(f"终点'{end_place}'有多个选择，建议明确具体位置")

            # 仍然尝试执行，但标记为可能失败

        # 记录调试信息
        self.debug_agent.log_function_debug({
            "phase": "pre_execution",
            "function_name": function_name,
            "function_args": function_args,
            "debug_score": debug_score,
            "issues": issues,
            "should_execute": should_execute,
            "session_id": session_id
        })

        return {
            "debug_score": debug_score,
            "issues": issues,
            "should_execute": should_execute,
            "fallback_response": fallback_response
        }

    def _debug_function_call_post(self, function_name: str, function_args: dict,
                                  function_response: dict, session_id: str):
        """Function执行后的调试分析"""
        debug_score = 1.0
        issues = []

        # 检查执行结果
        if not function_response.get("status", False):
            debug_score = 0.5
            error_msg = function_response.get("error", "未知错误")
            issues.append(f"功能调用失败：{error_msg}")

            # 分析失败原因并提供建议
            suggestions = self._analyze_failure_and_suggest(function_name, function_args, error_msg)
            if suggestions:
                issues.extend(suggestions)

        # 记录调试信息
        self.debug_agent.log_function_debug({
            "phase": "post_execution",
            "function_name": function_name,
            "function_args": function_args,
            "function_response": function_response,
            "debug_score": debug_score,
            "issues": issues,
            "session_id": session_id
        })

        return {
            "debug_score": debug_score,
            "issues": issues
        }

    def _format_taxi_service_result(self, raw_result: dict) -> dict:
        """统一格式化打车服务的返回结果"""
        try:
            # 检查原始结果格式
            if not isinstance(raw_result, dict):
                return {
                    "status": False,
                    "error": "打车服务返回格式错误",
                    "raw_result": raw_result
                }

            # 统一格式化
            formatted_result = {
                "status": raw_result.get("status", 0) == 1,  # 1表示成功
                "message": raw_result.get("action_ret_msg", ""),
                "error_code": raw_result.get("err_code", ""),
                "error_message": raw_result.get("err_msg", ""),
                "time_cost": raw_result.get("time_cost", 0),
                "details": {}
            }

            # 提取详细信息
            if "params" in raw_result:
                params = raw_result["params"]
                formatted_result["details"] = {
                    "pickup_name": params.get("pickup_name", ""),
                    "pickup_location": {
                        "longitude": params.get("pickup_l", ""),
                        "latitude": params.get("pickup_r", "")
                    },
                    "destination_name": params.get("dest_name", ""),
                    "destination_location": {
                        "longitude": params.get("dest_l", ""),
                        "latitude": params.get("dest_r", "")
                    },
                    "sub_id": params.get("sub_id", ""),
                    "output": params.get("output", "")
                }

            # 保留原始结果用于调试
            formatted_result["raw_data"] = raw_result

            return formatted_result

        except Exception as e:
            return {
                "status": False,
                "error": f"格式化打车服务结果时出错: {str(e)}",
                "raw_result": raw_result
            }

    def _check_missing_parameters(self, function_name: str, function_args: dict) -> bool:
        """检查是否有缺失的必需参数"""
        for tool in self.tools:
            if tool["function"]["name"] == function_name:
                required_params = tool["function"]["parameters"].get("required", [])
                for param in required_params:
                    if param not in function_args or not function_args[param]:
                        return True
        return False

    def _get_missing_parameters(self, function_name: str, function_args: dict) -> list:
        """获取缺失的参数列表"""
        missing = []
        for tool in self.tools:
            if tool["function"]["name"] == function_name:
                required_params = tool["function"]["parameters"].get("required", [])
                for param in required_params:
                    if param not in function_args or not function_args[param]:
                        missing.append(param)
        return missing

    def _check_parameter_validity(self, function_name: str, function_args: dict) -> bool:
        """检查参数合理性"""
        if function_name == "call_taxi_service":
            start_place = function_args.get("start_place", "")
            end_place = function_args.get("end_place", "")

            # 检查是否是不合理的地点
            unrealistic_places = ["火星", "金星", "月球", "太阳", "外太空", "银河系"]
            if any(place in start_place for place in unrealistic_places) or \
               any(place in end_place for place in unrealistic_places):
                return True

            # 检查是否是同一地点
            if start_place.strip() == end_place.strip():
                return True

        return False

    def _get_parameter_validity_issues(self, function_name: str, function_args: dict) -> str:
        """获取参数合理性问题描述，使用大模型智能判断"""
        if function_name == "call_taxi_service":
            start_place = function_args.get("start_place", "")
            end_place = function_args.get("end_place", "")

            # 使用大模型进行智能判断
            return self._llm_analyze_location_validity(start_place, end_place)

        return "参数不合理"

    def _llm_analyze_location_validity(self, start_place: str, end_place: str) -> str:
        """使用大模型分析地点有效性"""
        if not self.bailian_client:
            # 回退到基础检查
            unrealistic_places = ["火星", "金星", "月球", "太阳", "外太空", "银河系"]
            if any(place in start_place for place in unrealistic_places) or \
               any(place in end_place for place in unrealistic_places):
                return "目的地超出服务范围（地球范围内）"
            if start_place.strip() == end_place.strip():
                return "起点和终点相同"
            return "参数可能不合理"

        try:
            prompt = f"""请分析以下打车路线的合理性：
            起点：{start_place}
            终点：{end_place}

            请判断：
            1. 这些地点是否是真实存在的地理位置？
            2. 是否适合打车服务？
            3. 如果不合理，具体原因是什么？

            如果地点模糊（如"机场"、"大悦城"），建议使用POI搜索获取具体位置。
            请简洁回答，只说明主要问题。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个地理位置分析专家，专门判断地点的合理性。'},
                    {'role': 'user', 'content': prompt}
                ]
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            # 回退到基础检查
            return f"无法验证地点合理性：{str(e)}"

    def _analyze_failure_and_suggest(self, function_name: str, function_args: dict, error_msg: str) -> list:
        """分析失败原因并提供建议"""
        suggestions = []

        if "网络" in error_msg or "连接" in error_msg:
            suggestions.append("建议：检查网络连接后重试")

        if "API" in error_msg or "密钥" in error_msg:
            suggestions.append("建议：检查API密钥配置")

        if function_name == "call_taxi_service":
            if "地点" in error_msg or "位置" in error_msg:
                suggestions.append("建议：请提供更具体的地址信息")

        if function_name.startswith("mcp_"):
            suggestions.append("建议：尝试使用更常见的地点名称")

        return suggestions

    def _optimize_response_brevity(self, response: str) -> str:
        """优化回复简洁性"""
        # 移除重复的句子
        sentences = response.split('。')
        unique_sentences = []
        seen = set()

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and sentence not in seen:
                unique_sentences.append(sentence)
                seen.add(sentence)

        # 简化常见表达
        optimized = '。'.join(unique_sentences)

        # 替换冗长表达
        replacements = {
            "非常感谢您的": "感谢",
            "如果您还有其他需要帮助的地方": "如需其他帮助",
            "请随时告诉我": "请告知",
            "我很乐意为您": "我将为您",
            "为了更好地为您提供帮助": "为了帮助您",
            "请您提供": "请提供",
            "如果您需要": "如需",
            "我可以帮助您": "我可以帮您"
        }

        for old, new in replacements.items():
            optimized = optimized.replace(old, new)

        return optimized

    def _analyze_out_of_scope_request(self, request: str) -> str:
        """使用大模型和联网搜索分析超出范围的请求"""
        if not self.bailian_client:
            # 回退到基础分析
            return self._basic_scope_analysis(request)

        try:
            # 使用大模型进行智能分析
            prompt = f"""请分析以下用户请求是否属于打车服务范围：

                    用户请求：{request}

                    打车服务范围包括：
                    1. 地点查询（地理编码、POI搜索）
                    2. 打车叫车服务
                    3. 城市代码查询

                    请判断：
                    1. 这个请求是否在服务范围内？
                    2. 如果不在，属于什么类型的请求？
                    3. 是否可以通过现有工具部分满足？

                    请简洁回答分析结果。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个服务范围分析专家。'},
                    {'role': 'user', 'content': prompt}
                ],
                extra_body={
                    "enable_search": True  # 启用联网搜索
                }
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"分析失败，回退到基础判断：{self._basic_scope_analysis(request)}"

    def _basic_scope_analysis(self, request: str) -> str:
        """基础的范围分析（无需大模型）"""
        request_lower = request.lower()

        if any(keyword in request_lower for keyword in ["盈利", "财报", "股价", "业绩"]):
            return "财务信息查询 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["天气", "气温", "下雨"]):
            return "天气查询 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["电影", "音乐", "娱乐"]):
            return "娱乐推荐 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["机票", "酒店", "旅游"]):
            return "旅游服务 - 超出打车服务范围"
        else:
            return "未知请求类型 - 可能超出服务范围"

    def _provide_intelligent_suggestions(self, request: str) -> str:
        """使用大模型和联网搜索提供智能建议"""
        if not self.bailian_client:
            # 回退到基础建议
            return self._basic_suggestions(request)

        try:
            # 使用大模型生成智能建议
            prompt = f"""用户请求：{request}

这个请求超出了打车服务范围。请提供以下建议：

1. 推荐合适的工具、网站或APP来解决用户需求
2. 如果存在相关的MCP工具或API，建议用户配置使用
3. 评估是否可以在未来版本中支持此功能
4. 提供具体的操作步骤

请提供实用、具体的建议，包括推荐的平台名称和使用方法。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个智能助手建议专家，专门为用户提供替代解决方案。'},
                    {'role': 'user', 'content': prompt}
                ],
                extra_body={
                    "enable_search": True  # 启用联网搜索获取最新信息
                }
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"智能建议生成失败，基础建议：{self._basic_suggestions(request)}"

    def _basic_suggestions(self, request: str) -> str:
        """基础建议（无需大模型）"""
        analysis = self._basic_scope_analysis(request)

        if "财务信息" in analysis:
            return "建议使用专业财经网站或APP查询企业财务信息，如东方财富、同花顺等"
        elif "天气查询" in analysis:
            return "建议使用天气APP或询问语音助手获取天气信息"
        elif "娱乐推荐" in analysis:
            return "建议使用豆瓣、猫眼等娱乐平台获取推荐"
        elif "旅游服务" in analysis:
            return "建议使用携程、去哪儿等旅游平台预订机票酒店"
        else:
            return "抱歉，我专注于打车和地点查询服务。如需其他帮助，建议使用相应的专业平台"

    def _handle_scene_function(self, function_name: str, args: dict) -> dict:
        """处理场景相关的function调用"""
        # 这里可以根据不同场景实现不同逻辑
        return {
            "status": True,
            "data": args,
            "message": f"成功处理场景: {function_name}"
        }

    # 保持其他原有方法不变...
    def process_user_input(self, user_input: str) -> str:
        # 原有实现...
        pass

    def _detect_intent(self, text: str) -> str:
        # 原有实现...
        pass

    def _extract_parameters(self, text: str, intent: str) -> Dict:
        # 原有实现...
        pass

    def _check_missing_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _prepare_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _generate_success_response(self, intent: str, result: Dict) -> str:
        # 原有实现...
        pass

    def _handle_error(self, intent: str, error_result: Dict) -> str:
        # 原有实现...
        pass

    def _extract_nearby_poi_candidates(self, result: Dict) -> List[Dict]:
        """从经纬度转POI结果中提取候选项"""
        candidates = []
        if result.get("status") and result.get("data", {}).get("nearby_pois"):
            pois = result["data"]["nearby_pois"][:5]  # 取前5个
            for i, poi in enumerate(pois, 1):
                candidates.append({
                    "index": i,
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", ""),
                    "distance": poi.get("distance", 0),
                    "location": f"{poi.get('longitude', '')},{poi.get('latitude', '')}"
                })
        return candidates

    def _extract_similar_poi_candidates(self, result: Dict) -> List[Dict]:
        """从POI推荐结果中提取候选项"""
        candidates = []
        if result.get("status") and result.get("data", {}).get("similar_pois"):
            pois = result["data"]["similar_pois"][:5]  # 取前5个
            for i, poi in enumerate(pois, 1):
                candidates.append({
                    "index": i,
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", ""),
                    "distance": poi.get("distance", 0),
                    "similarity_score": poi.get("similarity_score", 0),
                    "location": f"{poi.get('longitude', '')},{poi.get('latitude', '')}"
                })
        return candidates

    def _analyze_reverse_geocode_result(self, result: Dict, user_intent: str) -> List[str]:
        """分析经纬度转POI结果"""
        suggestions = []
        if result.get("status"):
            data = result.get("data", {})
            poi_count = data.get("total_count", 0)
            if poi_count > 0:
                suggestions.append(f"在该位置附近找到{poi_count}个兴趣点")
                if poi_count > 5:
                    suggestions.append("显示了最相关的前5个结果")
            else:
                suggestions.append("该位置附近暂无已知的兴趣点")
        else:
            suggestions.append("无法获取该位置的周边信息")
        return suggestions

    def _analyze_poi_recommendation_result(self, result: Dict, user_intent: str) -> List[str]:
        """分析POI推荐结果"""
        suggestions = []
        if result.get("status"):
            data = result.get("data", {})
            original_poi = data.get("original_poi", {})
            similar_count = len(data.get("similar_pois", []))

            if similar_count > 0:
                suggestions.append(f"基于'{original_poi.get('name', '')}'找到{similar_count}个相似地点")
                suggestions.append("推荐结果按相似度排序")
            else:
                suggestions.append(f"'{original_poi.get('name', '')}'附近暂无相似的地点")
        else:
            suggestions.append("无法找到相似的地点推荐")
        return suggestions

class MainAgent:
    """保持向后兼容的主控Agent"""
    def __init__(self):
        self.enhanced_agent = EnhancedTaxiAgent()

    def process_user_input(self, user_input: str) -> str:
        return self.enhanced_agent.process_message(user_input)


def test_enhanced_taxi_agent():
    """测试增强版打车Agent"""
    print("=== 测试增强版打车Agent ===")

    agent = EnhancedTaxiAgent()

    # 测试用例
    test_cases = [
        "西湖在哪里？",
        "帮我查一下杭州的城市代码",
        "北京有哪些星巴克？",
        "经纬度116.397428,39.90923附近有什么？",
        "推荐一些北京上地星巴克附近的咖啡店",
        "我要从康德大厦打车到太阳宫",
        "从北京站到首都机场，要舒适型车辆"
    ]

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        try:
            response = agent.process_message(test_input, f"test_session_{i}")
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 50)


def test_call_taxi_service_format():
    """测试 call_taxi_service 的格式化输出"""
    print("\n=== 测试 call_taxi_service 格式化输出 ===")

    agent = EnhancedTaxiAgent()

    # 直接测试打车服务
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "test_session"
        )

        print("格式化后的结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    # 测试增强版Agent
    test_enhanced_taxi_agent()

    # 测试打车服务格式化
    test_call_taxi_service_format()

    # 保持向后兼容
    print("\n=== 向后兼容测试 ===")
    agent = MainAgent()
    print(agent.process_user_input("我要从康德大厦打车到大悦城"))
