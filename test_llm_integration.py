#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型集成测试
测试 Function Calling 和对话上下文
"""

import json
import uuid
import time
import os
from taxi_agent_system import EnhancedTaxiAgent


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def test_single_conversation():
    """测试单轮对话"""
    print("=== 测试单轮对话 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    test_messages = [
        "康德大厦在哪里？",
        "我要从康德大厦打车到太阳宫",
        "北京有哪些星巴克？",
        "帮我查一下杭州的城市代码"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- 测试 {i}: {message} ---")
        print(f"Session ID: {session_id}")
        
        try:
            response = agent.process_message(message, session_id)
            print(f"✅ 回复: {response}")
            
            # 检查状态
            status = agent.state.current_status
            print(f"📊 状态: {status['status']}")
            if status.get('error'):
                print(f"❌ 错误: {status['error']}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            # 使用 DebugAgent 分析错误
            agent.debug_agent.log_error(str(e), {
                "message": message,
                "session_id": session_id
            })
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")
        
        print("-" * 50)
        time.sleep(1)  # 避免请求过快


def test_context_conversation():
    """测试上下文对话"""
    print("\n=== 测试上下文对话 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟连续对话
    conversation_flow = [
        "你好，我想了解一下康德大厦的位置",
        "那我要从康德大厦打车去太阳宫",
        "车辆偏好选择舒适型",
        "谢谢"
    ]
    
    print(f"Session ID: {session_id}")
    
    for i, message in enumerate(conversation_flow, 1):
        print(f"\n--- 对话轮次 {i} ---")
        print(f"用户: {message}")
        
        try:
            response = agent.process_message(message, session_id)
            print(f"助手: {response}")
            
            # 显示上下文长度
            if session_id in agent.context:
                context_length = len(agent.context[session_id])
                print(f"📝 上下文长度: {context_length} 条消息")
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
            agent.debug_agent.log_error(str(e), {
                "message": message,
                "session_id": session_id,
                "conversation_turn": i
            })
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")
        
        time.sleep(1)


def test_multiple_sessions():
    """测试多个会话"""
    print("\n=== 测试多个会话 ===")
    
    agent = EnhancedTaxiAgent()
    
    sessions = [
        {
            "id": generate_session_id(),
            "messages": ["康德大厦在哪里？", "我要打车去机场"]
        },
        {
            "id": generate_session_id(), 
            "messages": ["北京有哪些星巴克？", "帮我查一下上海的城市代码"]
        }
    ]
    
    for session_info in sessions:
        session_id = session_info["id"]
        messages = session_info["messages"]
        
        print(f"\n--- 会话 {session_id} ---")
        
        for i, message in enumerate(messages, 1):
            print(f"轮次 {i}: {message}")
            
            try:
                response = agent.process_message(message, session_id)
                print(f"回复: {response}")
                
            except Exception as e:
                print(f"❌ 失败: {e}")
                agent.debug_agent.log_error(str(e), {
                    "message": message,
                    "session_id": session_id
                })
            
            time.sleep(0.5)
    
    # 显示所有会话状态
    print(f"\n📊 总共管理 {len(agent.context)} 个会话")
    for sid in agent.context.keys():
        print(f"  - {sid}: {len(agent.context[sid])} 条消息")


def test_function_calling_direct():
    """直接测试 Function Calling"""
    print("\n=== 直接测试 Function Calling ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试各种工具
    function_tests = [
        {
            "name": "call_taxi_service",
            "args": {
                "start_place": "康德大厦",
                "end_place": "太阳宫",
                "car_prefer": "舒适型"
            }
        },
        {
            "name": "mcp_geocode_address",
            "args": {
                "address": "康德大厦",
                "city": "北京"
            }
        },
        {
            "name": "mcp_get_city_code",
            "args": {
                "city_name": "北京"
            }
        },
        {
            "name": "mcp_search_poi",
            "args": {
                "keyword": "星巴克",
                "city": "北京"
            }
        }
    ]
    
    for test in function_tests:
        print(f"\n--- 测试 {test['name']} ---")
        print(f"参数: {json.dumps(test['args'], ensure_ascii=False)}")
        
        try:
            result = agent._execute_function(
                test['name'],
                test['args'],
                session_id
            )
            
            print(f"✅ 执行成功")
            print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            agent.debug_agent.log_error(str(e), {
                "function": test['name'],
                "args": test['args']
            })
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")


def check_api_configuration():
    """检查API配置"""
    print("=== API 配置检查 ===")
    
    amap_key = os.getenv("AMAP_API_KEY")
    bailian_key = os.getenv("BAILIAN_API_KEY")
    
    print(f"AMAP_API_KEY: {'✅ 已配置' if amap_key else '❌ 未配置'}")
    print(f"BAILIAN_API_KEY: {'✅ 已配置' if bailian_key else '❌ 未配置'}")
    
    if not amap_key:
        print("⚠️  高德地图工具将无法使用")
    if not bailian_key:
        print("⚠️  AI对话功能将无法使用")
    
    return bool(amap_key), bool(bailian_key)


def analyze_system_state(agent):
    """分析系统状态"""
    print("\n=== 系统状态分析 ===")
    
    # 执行历史
    history = agent.state.execution_history
    print(f"📊 执行历史: {len(history)} 条记录")
    
    if history:
        # 最近的执行
        recent = history[-3:]  # 最近3条
        for record in recent:
            status_icon = "✅" if record['status'] == 'success' else "❌"
            print(f"  {status_icon} {record['action']} - {record['status']}")
    
    # 错误日志
    errors = agent.debug_agent.error_log
    print(f"🐛 错误日志: {len(errors)} 条记录")
    
    if errors:
        recent_errors = errors[-2:]  # 最近2条错误
        for error in recent_errors:
            print(f"  ❌ {error['error']}")
    
    # 当前状态
    current = agent.state.current_status
    print(f"🔄 当前状态: {current['status']}")
    if current.get('error'):
        print(f"  错误: {current['error']}")


def provide_fix_suggestions(agent):
    """提供修正建议"""
    print("\n=== 修正建议 ===")
    
    errors = agent.debug_agent.error_log
    if not errors:
        print("✅ 系统运行正常，无需修正")
        return
    
    # 分析常见错误
    error_types = {}
    for error in errors:
        error_msg = error['error']
        if 'API' in error_msg or 'api_key' in error_msg:
            error_types['api_config'] = error_types.get('api_config', 0) + 1
        elif 'network' in error_msg.lower() or 'connection' in error_msg.lower():
            error_types['network'] = error_types.get('network', 0) + 1
        elif 'function' in error_msg.lower():
            error_types['function'] = error_types.get('function', 0) + 1
        else:
            error_types['other'] = error_types.get('other', 0) + 1
    
    print("🔧 建议修正措施:")
    
    if error_types.get('api_config', 0) > 0:
        print("1. API配置问题:")
        print("   - 检查环境变量 AMAP_API_KEY 和 BAILIAN_API_KEY")
        print("   - 确认API密钥有效且有足够权限")
        print("   - 验证API服务是否正常")
    
    if error_types.get('network', 0) > 0:
        print("2. 网络连接问题:")
        print("   - 检查网络连接")
        print("   - 确认防火墙设置")
        print("   - 尝试重新连接")
    
    if error_types.get('function', 0) > 0:
        print("3. 函数调用问题:")
        print("   - 检查函数参数格式")
        print("   - 验证必需参数是否提供")
        print("   - 确认函数定义正确")
    
    if error_types.get('other', 0) > 0:
        print("4. 其他问题:")
        print("   - 查看详细错误日志")
        print("   - 检查系统资源")
        print("   - 考虑重启服务")


def main():
    """主测试函数"""
    print("大模型集成测试")
    print("=" * 60)
    
    # 1. 检查API配置
    has_amap, has_bailian = check_api_configuration()
    
    # 2. 直接测试 Function Calling
    test_function_calling_direct()
    
    # 3. 测试单轮对话
    if has_bailian:
        test_single_conversation()
    else:
        print("\n⚠️  跳过单轮对话测试 (需要 BAILIAN_API_KEY)")
    
    # 4. 测试上下文对话
    if has_bailian:
        test_context_conversation()
    else:
        print("\n⚠️  跳过上下文对话测试 (需要 BAILIAN_API_KEY)")
    
    # 5. 测试多会话
    if has_bailian:
        test_multiple_sessions()
    else:
        print("\n⚠️  跳过多会话测试 (需要 BAILIAN_API_KEY)")
    
    # 6. 分析系统状态
    agent = EnhancedTaxiAgent()
    analyze_system_state(agent)
    
    # 7. 提供修正建议
    provide_fix_suggestions(agent)
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
