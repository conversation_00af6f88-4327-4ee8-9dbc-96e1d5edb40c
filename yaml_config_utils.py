"""
YAML配置文件工具
提供读取config.yaml中main_process_system_prompt的便捷函数
"""

import re
import os
import yaml
from typing import Optional, Dict, Any


def get_main_process_system_prompt(config_path: str = "config.yaml") -> str:
    """
    从配置文件中获取main_process_system_prompt
    
    Args:
        config_path: 配置文件路径，默认为 "config.yaml"
        
    Returns:
        str: 系统提示词内容，如果读取失败返回空字符串
        
    Example:
        >>> prompt = get_main_process_system_prompt()
        >>> print(f"提示词长度: {len(prompt)}")
    """
    try:
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return ""
        
        with open(config_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 尝试标准YAML解析
        try:
            config = yaml.safe_load(content)
            if isinstance(config, dict) and 'main_process_system_prompt' in config:
                return config['main_process_system_prompt']
        except yaml.YAMLError:
            pass
        
        # 如果标准YAML解析失败，尝试特殊格式解析
        # 格式: "main_process_system_prompt" : '内容'
        pattern = r'"main_process_system_prompt"\s*:\s*\'(.*?)\'\s*$'
        match = re.search(pattern, content, re.DOTALL | re.MULTILINE)
        
        if match:
            prompt_content = match.group(1)
            # 处理转义字符
            prompt_content = prompt_content.replace('\\n', '\n')
            return prompt_content
        
        return ""
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return ""


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """
    加载完整的配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    try:
        if not os.path.exists(config_path):
            return {}
        
        with open(config_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 尝试标准YAML解析
        try:
            config = yaml.safe_load(content)
            if isinstance(config, dict):
                return config
        except yaml.YAMLError:
            pass
        
        # 特殊格式解析
        config = {}
        pattern = r'"main_process_system_prompt"\s*:\s*\'(.*?)\'\s*$'
        match = re.search(pattern, content, re.DOTALL | re.MULTILINE)
        
        if match:
            prompt_content = match.group(1)
            prompt_content = prompt_content.replace('\\n', '\n')
            config['main_process_system_prompt'] = prompt_content
        
        return config
        
    except Exception as e:
        print(f"❌ 加载配置失败: {str(e)}")
        return {}


def save_standard_yaml(config: Dict[str, Any], output_path: str = "config_standard.yaml"):
    """
    保存为标准YAML格式
    
    Args:
        config: 配置字典
        output_path: 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as file:
            yaml.dump(config, file, default_flow_style=False, allow_unicode=True, indent=2)
        print(f"✅ 标准YAML已保存到: {output_path}")
    except Exception as e:
        print(f"❌ 保存标准YAML失败: {str(e)}")


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self._config = None
        self.load()
    
    def load(self):
        """加载配置"""
        self._config = load_config(self.config_path)
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        if self._config is None:
            self.load()
        return self._config.get('main_process_system_prompt', '')
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        if self._config is None:
            self.load()
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置项"""
        if self._config is None:
            self._config = {}
        self._config[key] = value
    
    def save(self, output_path: Optional[str] = None):
        """保存配置"""
        if output_path is None:
            output_path = self.config_path
        
        if self._config:
            save_standard_yaml(self._config, output_path)
    
    def reload(self):
        """重新加载配置"""
        self.load()


# 便捷函数
def quick_get_prompt() -> str:
    """快速获取系统提示词的便捷函数"""
    return get_main_process_system_prompt()


def test_config_utils():
    """测试配置工具"""
    print("🔧 测试配置工具")
    print("=" * 40)
    
    # 测试直接函数
    print("1. 测试直接函数:")
    prompt = get_main_process_system_prompt()
    print(f"   提示词长度: {len(prompt)}")
    print(f"   前100字符: {prompt[:100]}...")
    
    # 测试配置管理器
    print("\n2. 测试配置管理器:")
    manager = ConfigManager()
    prompt2 = manager.get_system_prompt()
    print(f"   提示词长度: {len(prompt2)}")
    print(f"   是否一致: {prompt == prompt2}")
    
    # 测试完整配置加载
    print("\n3. 测试完整配置:")
    config = load_config()
    print(f"   配置项数量: {len(config)}")
    print(f"   配置键: {list(config.keys())}")
    
    print("\n✅ 测试完成")


if __name__ == "__main__":
    test_config_utils()
