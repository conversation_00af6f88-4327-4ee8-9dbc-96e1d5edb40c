import json
import time
import requests
from composio.tools.local.greptile.actions.codequery import message
import re

class LangflowAgent:
    api_addr = "http://172.16.3.187:7860"
    flow_id = "847fdb28-92e5-4868-bf6b-c055988b012e"
    # "63b71e9f-651f-4f1e-8e65-a166d0026e66"

    def __init__(self):
        self.api_addr = "http://172.16.3.187:7860"
        self.flow_id = "847fdb28-92e5-4868-bf6b-c055988b012e"
        # "63b71e9f-651f-4f1e-8e65-a166d0026e66"
        # self.flow_id = "9aa48896-9218-4435-9be5-aed9aaccd4a4"

    def parse_duration(self,duration_str):
        # 使用正则提取数值部分（整数或小数）
        match = re.search(r"(\d+\.?\d*)", duration_str)
        if not match:
            raise ValueError(f"无法解析 duration: {duration_str}")

        duration = float(match.group(1))  # 转换为浮点数

        # 判断单位
        if "ms" in duration_str:
            return duration / 1000  # 毫秒转换为秒
        return duration  # 默认返回秒
    ##开始执行工作流
    def create_chat(self, req_id, user_id, global_conversation_id, chat_msg, target, custom_var={}, conversation_id=""):

        # url = f"{self.api_addr}/api/v1/build/{self.flow_id}/flow?&start_component_id=ChatInput-Iyun4&log_builds=true"
        url = f"{self.api_addr}/api/v1/build/{self.flow_id}/flow?log_builds=true"
        input_data = {
            "input_value": chat_msg
        }

        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            "x-api-key":"sk-_vPAbFHHSt3H1C44JCgq-fN8UNDB8sscRyvFr7N49_w",

        }
        try:
            response = requests.post(url, json={"inputs": input_data}, headers=headers)
            response.raise_for_status()  # 确保请求成功

            response_json = response.json()

            # 解析 job_id
            job_id = response_json.get("job_id", None)
            if job_id:
                return job_id
            else:
                print("API 响应中未找到 job_id")
                return None

        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return None

    ##获取工作流的节点并获取支持参数及返回值
    def wait_chat_finish(self, req_id, user_id, global_conversation_id, conversation_id, chat_id, target, create_code,):
        url = f"{self.api_addr}/api/v1/build/{chat_id}/events"
        headers = {"accept": "application/json"}
        result = {}
        try:
            with requests.get(url, headers=headers, stream=True) as response:
                response.raise_for_status()  # 确保请求成功
                sum_duration = 0
                for line in response.iter_lines():
                    if line.strip():  # 忽略空行
                        try:
                            event = json.loads(line)
                            # print(event)
                            build_data = event.get("data", {}).get("build_data", {})
                            data = build_data.get("data")
                            ##计算执行时间
                            id = build_data.get("id") ##查询id
                            if id and "duration" in data:
                                duration_str = data["duration"]
                                # 移除 "ms" 并转换为整数
                                try:
                                    duration = self.parse_duration(duration_str)
                                    sum_duration += duration
                                except ValueError:
                                    print(f"无法转换 duration: {duration_str}")  # 记录错误值
                            valid = build_data.get("valid")
                            if valid is False:
                                message = (
                                    build_data.get("data", {})
                                    .get("outputs", {})
                                    .get("output", {})
                                    .get("message", {})
                                )
                                errorMessage = message.get("errorMessage", "未提供错误信息")
                                if errorMessage:
                                    result["status"] = 1
                                    result["err_code"] = "fail"
                                    result["err_msg"] = errorMessage
                                    result["h"]
                                    return result  # 立即返回，避免继续等待
                            # 确保 next_vertices_ids 是列表且非空
                            next_vertices_ids = build_data.get("next_vertices_ids")
                            if isinstance(next_vertices_ids, list) and next_vertices_ids:
                                next_vertex_id = next_vertices_ids[0]  # 取列表的第一个元素
                            else:
                                next_vertex_id = None  # 如果 next_vertices_ids 为空或 None，则设为 None
                            if id and id.startswith("JSONToDataComponent") and next_vertex_id and next_vertex_id.startswith("ParseData"):
                                data = build_data.get("data", {}).get("outputs", {}).get("data", {})
                                # 确保 text 是字符串
                                message_str = data.get("message")
                                if isinstance(message_str, str):
                                    try:
                                        message = json.loads(message_str)  # 解析 JSON 字符串
                                    except json.JSONDecodeError:
                                        print(f"解析 message 失败: {data}")
                                        message = []
                                else:
                                    message = message_str  # 如果已经是对象，直接赋值

                                # # 确保 message 是列表，并且有数据
                                # if isinstance(message, list) and len(message) > 0:
                                #     message = message[0]  # 取第一个元素（字典）
                                # else:
                                #     message = {}

                                output = message.get("output")
                                status = message.get("status")
                                err_msg = message.get("err_msg")
                                code = message.get("code")
                                detail_info = message.get("detail_info")
                                params = message.get("params")
                                result["action_ret_msg"] = output
                                result["status"] = status
                                result["err_msg"] = err_msg
                                result["err_code"] = code
                                result["detail_info"] = detail_info
                                result["params"] = params
                            if id and id.startswith("QwenComponent"):
                                data = build_data.get("data", {}).get("outputs", {}).get("data", {})
                                message = data.get("message", {})
                                input_tokens = message.get("input_tokens")
                                output_tokens = message.get("output_tokens")
                                total_tokens = message.get("total_tokens")
                                # 存入 result
                                result["input_tokens"] = input_tokens
                                result["output_tokens"] = output_tokens
                                result["total_tokens"] = total_tokens
                        except json.JSONDecodeError as e:
                            print(f"JSON 解析错误: {e}, 原始数据: {line}")

        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
        result["time_cost"] = sum_duration
        return result

    def kill_chat(self, req_id, user_id, global_conversation_id, conversation_id, chat_id):
        cancel_url = f"{self.api_addr}/api/v1/build/{chat_id}/cancel"
        headers = {"accept": "application/json"}
        try:
            response = requests.post(cancel_url, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
            return response.json()  # 返回 JSON 响应
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}  # 发生异常时返回错误信息


def call_taxi_service(start_place, end_place, car_prefer="",
                     uid="1923357228652298240",
                     coords="116.30702, 40.040954",
                     intent_id="10007",
                     req_id="497efdc8-534a-42a1-9211-2d57ebf6df70",
                     conversation_id="1492dac5-c898-4e81-b521-d82606c023c1_1750264990146",
                     app_id="cn.caocaokeji.user",
                     ts="1750264998.105795",
                     scene_id=4,
                     order=4,
                     last_scene_id="-1",
                     last_scene_sub_id="-1",
                     start_alias="",
                     end_alias="",
                     talk="",
                     input="",
                     context=None):
    """
    调用出租车服务的函数模型

    Args:
        start_place (str): 出发地点
        end_place (str): 目的地
        car_prefer (str): 车辆偏好，默认为空
        其他参数: 保持原有默认值

    Returns:
        dict: 包含服务调用结果的字典
    """
    start_time = time.time()

    # 根据模版生成聊天消息
    chat_message = f"打个车从{start_place}出发到{end_place}"
    hist_chat = f"用户:{chat_message}"
    ori_chat_msg = chat_message

    # 如果没有提供context，使用默认值
    if context is None:
        context = {
            "req_id": req_id,
            "uid": uid,
            "intent_id": intent_id,
            "conversation_id": conversation_id,
            "coords": coords,
            "app_id": app_id,
            "conversation_ts": conversation_id.split('_')[-1] if '_' in conversation_id else "1750264990146",
            "token": "eyJhbGciOiJIUzM4NCJ9.eyJ1c2VySWQiOjE5MjMzNTcyMjg2NTIyOTgyNDAsImRldmljZUlkIjoiMDQ3MkI5RkQ3NDY2NDVBNUI2RkI3OURCRTAyNTc3NkZiMzcyZjgxZDFlYjdhMTkyZDNmYzJmYzcyODk2MGIxNyIsImF1dGhvcml0aWVzIjpbeyJhdXRob3JpdHkiOiJST0xFX0dFRUsifV0sInVzZXJuYW1lIjoidXNlcl9jMDdjY2IxMTI5NDk0NjBiOTVhNWEyMGRjMmY2MmQyOCIsImV4cCI6MTc1MTk5MjQyMn0.Q0Re98FZDZBRuPzUj-mBKj-urogM8WJh9rnVHT5gfHB2D-5aalI1AEFKvg-Qqkar",
            "pro_ver_id": "0"
        }

    # 构建输入数据
    input_data = {
        "start_place": start_place,
        "start_alias": start_alias,
        "end_place": end_place,
        "end_alias": end_alias,
        "car_prefer": car_prefer,
        "talk": talk,
        "order": order,
        "last_scene_id": last_scene_id,
        "last_scene_sub_id": last_scene_sub_id,
        "uid": uid,
        "coords": coords,
        "intent_id": intent_id,
        "req_id": req_id,
        "conversation_id": conversation_id,
        "input": input,
        "hist_chat": hist_chat,
        "app_id": app_id,
        "ts": ts,
        "scene_id": scene_id,
        "ori_chat_msg": ori_chat_msg,
        "context": context
    }

    # 将输入数据转换为JSON字符串
    input_value = json.dumps(input_data, ensure_ascii=False)

    # 创建LangflowAgent实例
    agent = LangflowAgent()

    try:
        # 启动任务并获取 job_id
        job_id = agent.create_chat("", "", "", input_value, "", {}, "")
        if not job_id:
            return {
                "status": 1,
                "err_code": "fail",
                "err_msg": "Failed to create chat job",
                "time_cost": time.time() - start_time
            }

        print(f"Job ID: {job_id}")

        # 等待任务完成
        finish_result = agent.wait_chat_finish("", "", "", "", job_id, "", "")
        print(f"finish的结果为{finish_result}")

        # 终止任务
        kill_result = agent.kill_chat("", "", "", "", job_id)
        print(f"kill的结果为{kill_result}")

        # 计算总耗时
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"程序执行时间: {elapsed_time:.2f} 秒")

        # 添加总耗时到结果中
        if isinstance(finish_result, dict):
            finish_result["total_elapsed_time"] = elapsed_time

        return finish_result

    except Exception as e:
        return {
            "status": 1,
            "err_code": "exception",
            "err_msg": str(e),
            "time_cost": time.time() - start_time
        }


if __name__ == "__main__":
    # 输入数据
    start_time = time.time()  # 记录开始时间
    input_value = '''{"start_place": "康德大厦", "start_alias": "", "end_place": "机场", "end_alias": "", "car_prefer": "", "talk": "", "order": 4, "last_scene_id": "-1", "last_scene_sub_id": "-1", "uid": "1923357228652298240", "coords": "116.30702, 40.040954", "intent_id": "10007", "req_id": "497efdc8-534a-42a1-9211-2d57ebf6df70", "conversation_id": "1492dac5-c898-4e81-b521-d82606c023c1_1750264990146", "input": "", "hist_chat": "用户:打个车从康德大厦出发到机场", "app_id": "cn.caocaokeji.user", "ts": "1750264998.105795", "scene_id": 4, "ori_chat_msg": "打个车从康德大厦出发到机场", "context": {"req_id": "497efdc8-534a-42a1-9211-2d57ebf6df70", "uid": "1923357228652298240", "intent_id": "10007", "conversation_id": "1492dac5-c898-4e81-b521-d82606c023c1_1750264990146", "coords": "116.30702, 40.040954", "app_id": "cn.caocaokeji.user", "conversation_ts": "1750264990146", "token": "eyJhbGciOiJIUzM4NCJ9.eyJ1c2VySWQiOjE5MjMzNTcyMjg2NTIyOTgyNDAsImRldmljZUlkIjoiMDQ3MkI5RkQ3NDY2NDVBNUI2RkI3OURCRTAyNTc3NkZiMzcyZjgxZDFlYjdhMTkyZDNmYzJmYzcyODk2MGIxNyIsImF1dGhvcml0aWVzIjpbeyJhdXRob3JpdHkiOiJST0xFX0dFRUsifV0sInVzZXJuYW1lIjoidXNlcl9jMDdjY2IxMTI5NDk0NjBiOTVhNWEyMGRjMmY2MmQyOCIsImV4cCI6MTc1MTk5MjQyMn0.Q0Re98FZDZBRuPzUj-mBKj-urogM8WJh9rnVHT5gfHB2D-5aalI1AEFKvg-Qqkar", "pro_ver_id": "0"}}'''
    
    # '''{"uid": "1900437112298143744", "coords": "116.312564,40.059029", "intent_id": "10002", "req_id": "970f8264-870f-48bd-878c-ec66f50c5bda", "conversation_id": "1111111", "input": "true", "hist_chat": "用户:帮我点杯咖啡; 你:正在帮你点咖啡，请稍等。; 你:你还要点开拓大厦店的咖啡么？; 用户:好的", "app_id": "com.lucky.luckyclient", "ts": "1744020268.1466365", "scene_id": 2, "context": {"req_id": "970f8264-870f-48bd-878c-ec66f50c5bda", "uid": "1900437112298143744", "intent_id": "10002", "conversation_id": "1111111", "coords": "116.312564,40.059029", "app_id": "com.lucky.luckyclient"}, "shop_name": "开拓大厦店", "add_dish": [{"dish_name": "咖啡", "dish_cnt": 1, "dish_prefer": []}], "delete_dish": [], "shop_prefer": [], "order_dish": true}'''
    # input_value = '''{"uid":"1904790616680693760","coords":"116.312564,40.059029","intent_id":"10006","req_id":"d4bd3e6d-3412-4e97-982e-c00e9feef211","conversation_id":"1111111","input":"播放我喜欢的歌","hist_chat":"","app_id":"","ts":"1742984963.7668955","scene_id":"1","rule_id":"1000273","rule_name":"播放我喜欢的歌","params":"{}","task_desc":"语音触发后，将会自动操作：【step1】打开网易云音乐【step2】进入“我的”页面【step3】点击“我喜欢的音乐”>【step4】点击播放按钮","data_sample":"","answer_sample":"这就为你播放","answer_type":"1","app_name":"网易云音乐","ask":""}'''
    agent = LangflowAgent()
    # 启动任务并获取 job_id
    job_id = agent.create_chat("", "", "", input_value, "", {}, "")
    print(job_id)
    # 等待 3 秒
    finish = agent.wait_chat_finish("", "", "", "", job_id, "", "")
    print(f"finish的结果为{finish}")
    result = agent.kill_chat("", "", "", "", job_id)
    print(f"result的结果为{result}")
    end_time = time.time()  # 记录结束时间
    elapsed_time = end_time - start_time  # 计算总耗时
    print(f"程序执行时间: {elapsed_time:.2f} 秒")

    print("\n" + "="*50)
    print("=== 测试新的函数模型 ===")
    print("="*50)

    # 测试新的函数模型
    result = call_taxi_service(
        start_place="康德大厦",
        end_place="机场",
        car_prefer=""
    )
    print(f"最终结果: {result}")
