{"metadata": {"generated_at": "2025-06-21T14:51:00.767888", "total_scenarios": 5, "scenario_types": [1, 2, 3, 4, 5], "generator_version": "1.0"}, "scenarios": [{"session_id": "fb7ae924-a20c-4e5d-a805-b20e2ea85c00", "scenario_type": 1, "scenario_name": "首次使用用户", "conversation": [{"turn": 1, "user_input": "能帮我叫车吗？", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:51:00.767378", "scenario_type": 1, "scenario_name": "首次使用用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 44}}, {"session_id": "70a0f7f1-0b47-43fa-8377-cb6b696e6dfd", "scenario_type": 2, "scenario_name": "有历史记录用户", "conversation": [{"turn": 1, "user_input": "我要舒适型车辆", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:51:00.767446", "scenario_type": 2, "scenario_name": "有历史记录用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 44}}, {"session_id": "acfe1a22-75d9-4f42-b50d-cf835abb752c", "scenario_type": 3, "scenario_name": "探索型用户", "conversation": [{"turn": 1, "user_input": "我想看看周边", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:51:00.767500", "scenario_type": 3, "scenario_name": "探索型用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 43}}, {"session_id": "5828e568-e8a4-4b84-875d-88e2ebd2b129", "scenario_type": 4, "scenario_name": "随意聊天用户", "conversation": [{"turn": 1, "user_input": "我心情不太好", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:51:00.767545", "scenario_type": 4, "scenario_name": "随意聊天用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 43}}, {"session_id": "c8e46e3b-9f25-409c-876b-05e493383625", "scenario_type": 5, "scenario_name": "超出能力范围用户", "conversation": [{"turn": 1, "user_input": "查一下明天的天气", "system_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "timestamp": "2025-06-21T14:51:00.767588", "scenario_type": 5, "scenario_name": "超出能力范围用户"}], "summary": {"total_turns": 1, "is_successful": false, "last_response": "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。", "conversation_length": 45}}]}