{"name": "taxi-chat-demo", "version": "1.0.0", "description": "智能打车助手 - React 聊天界面演示", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.4.0", "styled-components": "^6.0.0", "framer-motion": "^10.12.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "lucide-react": "^0.263.1", "recharts": "^2.7.2", "uuid": "^9.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}