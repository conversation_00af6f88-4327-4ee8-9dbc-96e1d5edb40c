#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
出租车服务函数模型使用示例

这个文件展示了如何使用改造后的 call_taxi_service 函数模型
"""

from langflow_api_V4 import call_taxi_service
import time


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 最简单的调用方式，只需要提供起点和终点
    result = call_taxi_service(
        start_place="康德大厦",
        end_place="机场"
    )
    
    print(f"调用结果: {result}")
    return result


def example_with_car_preference():
    """带车辆偏好的示例"""
    print("\n=== 带车辆偏好的示例 ===")
    
    result = call_taxi_service(
        start_place="北京站",
        end_place="首都机场",
        car_prefer="舒适型"
    )
    
    print(f"调用结果: {result}")
    return result


def example_with_custom_params():
    """自定义参数示例"""
    print("\n=== 自定义参数示例 ===")
    
    # 可以自定义更多参数
    result = call_taxi_service(
        start_place="三里屯",
        end_place="王府井",
        car_prefer="经济型",
        uid="custom_user_123",
        coords="116.456, 39.928",
        conversation_id="custom_conversation_456"
    )
    
    print(f"调用结果: {result}")
    return result


def batch_taxi_requests():
    """批量请求示例"""
    print("\n=== 批量请求示例 ===")
    
    requests = [
        {"start_place": "天安门", "end_place": "故宫", "car_prefer": ""},
        {"start_place": "颐和园", "end_place": "圆明园", "car_prefer": "舒适型"},
        {"start_place": "鸟巢", "end_place": "水立方", "car_prefer": "经济型"}
    ]
    
    results = []
    for i, req in enumerate(requests):
        print(f"\n处理第 {i+1} 个请求: {req['start_place']} -> {req['end_place']}")
        
        result = call_taxi_service(
            start_place=req["start_place"],
            end_place=req["end_place"],
            car_prefer=req["car_prefer"]
        )
        
        results.append(result)
        print(f"结果: {result}")
        
        # 避免请求过于频繁
        time.sleep(1)
    
    return results


def analyze_result(result):
    """分析调用结果"""
    print("\n=== 结果分析 ===")
    
    if isinstance(result, dict):
        print("结果类型: 字典")
        
        # 检查状态
        status = result.get("status")
        if status == 0:
            print("✅ 调用成功")
        else:
            print("❌ 调用失败")
            print(f"错误代码: {result.get('err_code')}")
            print(f"错误信息: {result.get('err_msg')}")
        
        # 显示关键信息
        if "action_ret_msg" in result:
            print(f"返回消息: {result['action_ret_msg']}")
        
        if "time_cost" in result:
            print(f"执行时间: {result['time_cost']:.2f} 秒")
        
        if "total_elapsed_time" in result:
            print(f"总耗时: {result['total_elapsed_time']:.2f} 秒")
        
        # 显示token使用情况
        if "input_tokens" in result:
            print(f"输入tokens: {result['input_tokens']}")
        if "output_tokens" in result:
            print(f"输出tokens: {result['output_tokens']}")
        if "total_tokens" in result:
            print(f"总tokens: {result['total_tokens']}")
    else:
        print(f"结果类型: {type(result)}")
        print(f"结果内容: {result}")


def main():
    """主函数"""
    print("出租车服务函数模型使用示例")
    print("=" * 50)
    
    try:
        # 基本使用示例
        result1 = example_basic_usage()
        analyze_result(result1)
        
        # 带车辆偏好的示例
        result2 = example_with_car_preference()
        analyze_result(result2)
        
        # 自定义参数示例
        result3 = example_with_custom_params()
        analyze_result(result3)
        
        # 批量请求示例（可选，注释掉以避免过多请求）
        # batch_results = batch_taxi_requests()
        # for i, result in enumerate(batch_results):
        #     print(f"\n批量请求 {i+1} 结果分析:")
        #     analyze_result(result)
        
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
