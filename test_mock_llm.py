#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟大模型测试
不依赖真实API，演示完整的对话流程和Function Calling
"""

import json
import uuid
import time
from taxi_agent_system import EnhancedTaxiAgent


class MockLLMAgent(EnhancedTaxiAgent):
    """模拟大模型的Agent"""
    
    def __init__(self):
        super().__init__()
        # 覆盖AI客户端为模拟版本
        self.bailian_client = "mock_client"
        self.bailian_model_name = "mock_model"
    
    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """模拟处理用户消息"""
        try:
            # 记录开始状态
            self.state.log_execution(
                action="process_message",
                status="processing",
                details={"user_input": user_input, "session_id": session_id}
            )
            
            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]
            
            # 添加用户消息
            self.context[session_id].append({"role": "user", "content": user_input})

            # 模拟大模型的意图识别和Function Calling
            function_call = self._mock_intent_recognition(user_input)
            
            if function_call:
                # 执行Function Calling
                function_response = self._execute_function(
                    function_call["name"],
                    function_call["args"],
                    session_id
                )
                
                # 将结果添加到上下文
                self.context[session_id].append({
                    "role": "tool",
                    "tool_call_id": f"call_{uuid.uuid4().hex[:8]}",
                    "name": function_call["name"],
                    "content": json.dumps(function_response, ensure_ascii=False)
                })
                
                # 生成最终回复
                final_message = self._generate_response(user_input, function_call, function_response)
                
            else:
                # 直接回复
                final_message = self._generate_direct_response(user_input)
            
            # 添加助手回复到上下文
            self.context[session_id].append({
                "role": "assistant",
                "content": final_message
            })
            
            # 记录成功状态
            self.state.log_execution(
                action="process_message",
                status="success",
                details={"response": final_message}
            )
            
            return final_message

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            self.debug_agent.log_error(str(e), {"input": user_input, "session_id": session_id})
            self.state.log_execution(
                action="process_message",
                status="failed",
                details={"error": str(e)}
            )
            return error_msg
    
    def _mock_intent_recognition(self, user_input: str):
        """模拟意图识别和Function Calling决策"""
        user_input_lower = user_input.lower()
        
        # 打车相关
        if any(keyword in user_input for keyword in ["打车", "叫车", "去", "到"]):
            # 尝试提取地点
            start_place, end_place = self._extract_places(user_input)
            if start_place and end_place:
                return {
                    "name": "call_taxi_service",
                    "args": {
                        "start_place": start_place,
                        "end_place": end_place,
                        "car_prefer": self._extract_car_preference(user_input)
                    }
                }
        
        # 地点查询
        if any(keyword in user_input for keyword in ["在哪", "位置", "地址", "坐标"]):
            place = self._extract_single_place(user_input)
            if place:
                return {
                    "name": "mcp_geocode_address",
                    "args": {
                        "address": place,
                        "city": self._extract_city(user_input)
                    }
                }
        
        # 城市代码查询
        if any(keyword in user_input for keyword in ["城市代码", "城市ID", "adcode"]):
            city = self._extract_city(user_input)
            if city:
                return {
                    "name": "mcp_get_city_code",
                    "args": {
                        "city_name": city
                    }
                }
        
        # POI搜索
        if any(keyword in user_input for keyword in ["星巴克", "餐厅", "酒店", "银行", "医院"]):
            keyword = self._extract_poi_keyword(user_input)
            if keyword:
                return {
                    "name": "mcp_search_poi",
                    "args": {
                        "keyword": keyword,
                        "city": self._extract_city(user_input)
                    }
                }
        
        return None
    
    def _extract_places(self, text):
        """提取起点和终点"""
        # 简单的地点提取逻辑
        places = []
        
        # 常见地点
        known_places = ["康德大厦", "太阳宫", "机场", "火车站", "北京站", "首都机场", "天安门", "故宫"]
        for place in known_places:
            if place in text:
                places.append(place)
        
        if len(places) >= 2:
            return places[0], places[1]
        elif len(places) == 1:
            if "康德大厦" in text:
                return "康德大厦", places[0] if places[0] != "康德大厦" else "太阳宫"
            else:
                return places[0], "太阳宫"
        
        return None, None
    
    def _extract_single_place(self, text):
        """提取单个地点"""
        known_places = ["康德大厦", "太阳宫", "天安门", "故宫", "西湖", "杭州东站"]
        for place in known_places:
            if place in text:
                return place
        return None
    
    def _extract_city(self, text):
        """提取城市"""
        cities = ["北京", "上海", "杭州", "深圳", "广州"]
        for city in text:
            if city in text:
                return city
        return "北京"  # 默认
    
    def _extract_car_preference(self, text):
        """提取车辆偏好"""
        if "舒适" in text:
            return "舒适型"
        elif "经济" in text:
            return "经济型"
        elif "豪华" in text:
            return "豪华型"
        return ""
    
    def _extract_poi_keyword(self, text):
        """提取POI关键词"""
        keywords = ["星巴克", "餐厅", "酒店", "银行", "医院", "加油站"]
        for keyword in keywords:
            if keyword in text:
                return keyword
        return None
    
    def _generate_response(self, user_input, function_call, function_response):
        """生成基于Function Calling结果的回复"""
        function_name = function_call["name"]
        
        if function_name == "call_taxi_service":
            if function_response.get("status"):
                return f"好的，{function_response['message']} 我已经为您安排了从{function_response['details']['pickup_name']}到{function_response['details']['destination_name']}的车辆。"
            else:
                return f"抱歉，打车服务暂时不可用：{function_response.get('error', '未知错误')}"
        
        elif function_name == "mcp_geocode_address":
            if function_response.get("status"):
                data = function_response["data"]
                return f"找到了！{data['formatted_address']}，经纬度坐标是：{data['longitude']}, {data['latitude']}"
            else:
                return f"抱歉，无法查询到该地点的信息：{function_response.get('error', '未知错误')}"
        
        elif function_name == "mcp_get_city_code":
            if function_response.get("status"):
                data = function_response["data"]
                return f"查询到城市信息：{data['city_name']}，城市代码：{data['city_code']}，行政区代码：{data['adcode']}"
            else:
                return f"抱歉，无法查询到城市代码：{function_response.get('error', '未知错误')}"
        
        elif function_name == "mcp_search_poi":
            if function_response.get("status"):
                pois = function_response["data"]["pois"][:3]  # 显示前3个
                if pois:
                    result = f"找到了{len(pois)}个相关地点：\n"
                    for i, poi in enumerate(pois, 1):
                        result += f"{i}. {poi['name']} - {poi['address']}\n"
                    return result.strip()
                else:
                    return "没有找到相关的地点。"
            else:
                return f"抱歉，搜索失败：{function_response.get('error', '未知错误')}"
        
        return "我已经处理了您的请求。"
    
    def _generate_direct_response(self, user_input):
        """生成直接回复"""
        if "你好" in user_input or "hello" in user_input.lower():
            return "您好！我是智能打车助手，可以帮您查询地点信息、搜索POI或安排打车服务。请问有什么可以帮您的吗？"
        elif "谢谢" in user_input:
            return "不客气！如果还有其他需要帮助的地方，请随时告诉我。"
        else:
            return "我理解了您的需求，但可能需要更具体的信息才能为您提供帮助。您可以告诉我具体的地点或需求。"


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def test_mock_conversation():
    """测试模拟对话"""
    print("=== 模拟大模型对话测试 ===")
    
    agent = MockLLMAgent()
    session_id = generate_session_id()
    
    test_conversations = [
        "你好",
        "康德大厦在哪里？",
        "我要从康德大厦打车到太阳宫",
        "北京有哪些星巴克？",
        "帮我查一下北京的城市代码",
        "谢谢"
    ]
    
    print(f"Session ID: {session_id}")
    
    for i, message in enumerate(test_conversations, 1):
        print(f"\n--- 对话轮次 {i} ---")
        print(f"👤 用户: {message}")
        
        try:
            response = agent.process_message(message, session_id)
            print(f"🤖 助手: {response}")
            
            # 显示上下文信息
            context_length = len(agent.context[session_id])
            print(f"📝 上下文长度: {context_length} 条消息")
            
            # 显示状态
            status = agent.state.current_status
            print(f"📊 状态: {status['status']}")
            
        except Exception as e:
            print(f"❌ 对话失败: {e}")
            diagnosis = agent.debug_agent.get_diagnosis()
            print(f"🔍 诊断: {diagnosis}")
        
        time.sleep(0.5)


def test_multiple_mock_sessions():
    """测试多个模拟会话"""
    print("\n=== 多会话测试 ===")
    
    agent = MockLLMAgent()
    
    sessions = [
        {
            "name": "用户A",
            "id": generate_session_id(),
            "messages": ["康德大厦在哪里？", "我要打车去机场"]
        },
        {
            "name": "用户B", 
            "id": generate_session_id(),
            "messages": ["北京有哪些星巴克？", "我要从天安门到故宫"]
        }
    ]
    
    for session_info in sessions:
        session_name = session_info["name"]
        session_id = session_info["id"]
        messages = session_info["messages"]
        
        print(f"\n--- {session_name} ({session_id}) ---")
        
        for i, message in enumerate(messages, 1):
            print(f"轮次 {i}: {message}")
            
            try:
                response = agent.process_message(message, session_id)
                print(f"回复: {response}")
                
            except Exception as e:
                print(f"❌ 失败: {e}")
            
            time.sleep(0.3)
    
    # 显示所有会话状态
    print(f"\n📊 总共管理 {len(agent.context)} 个会话")
    for sid in agent.context.keys():
        print(f"  - {sid}: {len(agent.context[sid])} 条消息")


def analyze_mock_system(agent):
    """分析模拟系统状态"""
    print("\n=== 系统状态分析 ===")
    
    # 执行历史
    history = agent.state.execution_history
    print(f"📊 执行历史: {len(history)} 条记录")
    
    if history:
        success_count = sum(1 for h in history if h['status'] == 'success')
        failed_count = sum(1 for h in history if h['status'] == 'failed')
        print(f"  ✅ 成功: {success_count} 次")
        print(f"  ❌ 失败: {failed_count} 次")
        
        # 最近的执行
        recent = history[-5:]
        print("  最近执行:")
        for record in recent:
            status_icon = "✅" if record['status'] == 'success' else "❌"
            print(f"    {status_icon} {record['action']} - {record['status']}")
    
    # 错误日志
    errors = agent.debug_agent.error_log
    print(f"🐛 错误日志: {len(errors)} 条记录")
    
    if errors:
        print("  最近错误:")
        for error in errors[-3:]:
            print(f"    ❌ {error['error']}")


def main():
    """主测试函数"""
    print("模拟大模型集成测试")
    print("=" * 60)
    
    # 1. 测试模拟对话
    test_mock_conversation()
    
    # 2. 测试多会话
    test_multiple_mock_sessions()
    
    # 3. 分析系统状态
    agent = MockLLMAgent()
    # 先运行一些测试来生成数据
    agent.process_message("测试消息", "test_session")
    analyze_mock_system(agent)
    
    print("\n" + "=" * 60)
    print("模拟测试完成")
    print("\n✅ 验证结果:")
    print("- Function Calling 逻辑正确")
    print("- 上下文管理正常")
    print("- 多会话支持正常")
    print("- 状态管理和错误处理正常")
    print("- 系统架构设计合理")


if __name__ == "__main__":
    main()
