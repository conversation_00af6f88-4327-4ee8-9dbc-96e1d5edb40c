#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户行为模拟器演示脚本
快速演示和测试用户行为模拟功能
"""

import os
import json
from user_behavior_simulator import UserBehaviorSimulator


def quick_demo():
    """快速演示"""
    print("=== 用户行为模拟器快速演示 ===\n")
    
    # 检查环境变量
    if not os.getenv("BAILIAN_API_KEY"):
        print("⚠️  警告: 未设置BAILIAN_API_KEY环境变量")
        print("将使用预设的回退问题进行演示\n")
    
    simulator = UserBehaviorSimulator()
    
    # 展示5种用户类型
    print("支持的用户类型:")
    for i, (scenario_type, scenario) in enumerate(simulator.user_scenarios.items(), 1):
        print(f"{scenario_type}. {scenario['name']}: {scenario['description']}")
    
    print("\n" + "="*60)
    
    # 为每种类型生成一个示例对话
    for scenario_type in range(1, 6):
        print(f"\n【场景 {scenario_type}】{simulator.user_scenarios[scenario_type]['name']}")
        print("-" * 40)
        
        # 创建会话
        session_id = simulator.generate_session_id()
        simulator.assign_scenario_to_session(session_id, scenario_type)
        
        # 生成首轮问题
        user_input = simulator.generate_user_input(session_id, "", 1)
        print(f"用户首轮输入: {user_input}")
        
        # 获取系统回复
        try:
            system_response = simulator.taxi_agent.process_user_input(user_input)
            print(f"系统回复: {system_response[:100]}..." if len(system_response) > 100 else f"系统回复: {system_response}")
        except Exception as e:
            print(f"系统回复出错: {e}")
        
        print()


def interactive_demo():
    """交互式演示"""
    print("=== 交互式用户行为模拟 ===\n")
    
    simulator = UserBehaviorSimulator()
    
    # 让用户选择场景类型
    print("请选择要模拟的用户类型:")
    for scenario_type, scenario in simulator.user_scenarios.items():
        print(f"{scenario_type}. {scenario['name']}")
    
    while True:
        try:
            choice = int(input("\n请输入场景编号 (1-5): "))
            if choice in simulator.user_scenarios:
                break
            else:
                print("请输入有效的场景编号 (1-5)")
        except ValueError:
            print("请输入数字")
    
    # 创建会话
    session_id = simulator.generate_session_id()
    simulator.assign_scenario_to_session(session_id, choice)
    
    scenario_name = simulator.user_scenarios[choice]['name']
    print(f"\n开始模拟 '{scenario_name}' 用户行为")
    print(f"会话ID: {session_id}")
    print("=" * 50)
    
    # 模拟对话
    context = ""
    for turn in range(1, 6):  # 最多5轮
        print(f"\n第 {turn} 轮对话:")
        
        # 生成用户输入
        user_input = simulator.generate_user_input(session_id, context, turn)
        print(f"🗣️  模拟用户: {user_input}")
        
        # 获取系统回复
        try:
            system_response = simulator.taxi_agent.process_user_input(user_input)
            print(f"🤖 系统回复: {system_response}")
        except Exception as e:
            print(f"❌ 系统错误: {e}")
            break
        
        # 更新上下文
        context = f"用户说：{user_input}\n系统回复：{system_response}"
        
        # 询问是否继续
        if turn < 5:
            continue_choice = input("\n是否继续下一轮? (y/n): ").lower()
            if continue_choice != 'y':
                break
    
    print("\n对话结束")


def batch_test():
    """批量测试"""
    print("=== 批量测试用户行为模拟 ===\n")
    
    simulator = UserBehaviorSimulator()
    
    # 询问测试参数
    try:
        sessions_per_type = int(input("每种用户类型测试几个会话? (默认2): ") or "2")
        max_turns = int(input("每个会话最多几轮对话? (默认3): ") or "3")
    except ValueError:
        sessions_per_type = 2
        max_turns = 3
    
    print(f"\n开始批量测试: 每种类型{sessions_per_type}个会话，每个会话最多{max_turns}轮")
    print("=" * 60)
    
    # 生成测试场景
    test_scenarios = []
    
    for scenario_type in range(1, 6):
        scenario_name = simulator.user_scenarios[scenario_type]['name']
        print(f"\n测试场景 {scenario_type}: {scenario_name}")
        
        for i in range(sessions_per_type):
            print(f"  会话 {i+1}/{sessions_per_type}...", end=" ")
            
            session_id = simulator.generate_session_id()
            simulator.assign_scenario_to_session(session_id, scenario_type)
            
            # 模拟对话
            conversation = simulator.simulate_conversation(session_id, max_turns)
            
            test_scenarios.append({
                "session_id": session_id,
                "scenario_type": scenario_type,
                "scenario_name": scenario_name,
                "conversation": conversation,
                "summary": simulator._generate_conversation_summary(conversation)
            })
            
            print("完成")
    
    # 分析结果
    print(f"\n测试完成! 共生成 {len(test_scenarios)} 个测试场景")
    analysis = simulator.analyze_test_results(test_scenarios)
    simulator.print_analysis_report(analysis)
    
    # 保存结果
    filename = simulator.save_test_results(test_scenarios)
    print(f"\n详细结果已保存到: {filename}")
    
    return test_scenarios


def generate_questions_only():
    """仅生成问题，不调用系统"""
    print("=== 生成用户问题示例 ===\n")
    
    simulator = UserBehaviorSimulator()
    
    print("为每种用户类型生成3个首轮问题示例:\n")
    
    for scenario_type in range(1, 6):
        scenario = simulator.user_scenarios[scenario_type]
        print(f"{scenario_type}. {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print("   示例问题:")
        
        for i in range(3):
            session_id = simulator.generate_session_id()
            simulator.assign_scenario_to_session(session_id, scenario_type)
            user_input = simulator.generate_user_input(session_id, "", 1)
            print(f"   - {user_input}")
        
        print()


def main():
    """主菜单"""
    print("🚗 用户行为模拟智能体")
    print("模拟5种不同类型的用户与打车系统的交互\n")
    
    while True:
        print("请选择功能:")
        print("1. 快速演示 (展示所有用户类型)")
        print("2. 交互式演示 (选择特定用户类型)")
        print("3. 批量测试 (生成测试报告)")
        print("4. 仅生成问题示例")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == "1":
            quick_demo()
        elif choice == "2":
            interactive_demo()
        elif choice == "3":
            batch_test()
        elif choice == "4":
            generate_questions_only()
        elif choice == "5":
            print("再见!")
            break
        else:
            print("无效选项，请重新选择")
        
        input("\n按回车键继续...")
        print("\n" + "="*60 + "\n")


if __name__ == "__main__":
    main()
