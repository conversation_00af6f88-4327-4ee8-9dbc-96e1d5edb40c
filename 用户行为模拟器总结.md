# 用户行为模拟智能体 - 项目总结

## 🎯 项目目标

构建一个智能体，使用大语言模型模拟用户的行为，生成多轮的问题，用于测试taxi_agent_system的智能水平。

## 📋 核心功能

### 1. 五种用户场景模拟

| 场景类型 | 用户特征 | 典型行为 |
|---------|---------|---------|
| **1. 首次使用用户** | 有明确目标但信息不完整 | "你好，我想打车"、"这个怎么用啊？" |
| **2. 有历史记录用户** | 熟悉系统，有明确需求 | "还是去上次那个地方"、"从康德大厦到太阳宫" |
| **3. 探索型用户** | 没有明确目的地，纯探索 | "附近有什么好玩的？"、"我想随便逛逛" |
| **4. 随意聊天用户** | 不着边际，话题跳跃 | "今天天气真好"、"你能聊天吗？" |
| **5. 超出能力范围用户** | 提出系统无法处理的需求 | "帮我订酒店"、"查明天天气" |

### 2. 智能对话生成

- **大模型驱动**: 使用百炼qwen-max模型生成自然语言
- **上下文感知**: 支持多轮对话上下文理解
- **语音特征**: 考虑口语化表达和停顿词
- **回退机制**: 无API时使用预设问题库

### 3. 测试分析功能

- **批量测试**: 自动生成多个测试场景
- **成功率统计**: 分析对话完成情况
- **轮次分析**: 统计平均交互轮数
- **分类报告**: 按用户类型分别统计

## 🏗️ 系统架构

```
用户行为模拟器 (UserBehaviorSimulator)
├── 场景管理
│   ├── 会话创建 (generate_session_id)
│   ├── 场景分配 (assign_scenario_to_session)
│   └── 会话信息 (get_session_info)
├── 输入生成
│   ├── 智能生成 (generate_user_input)
│   ├── 提示词构建 (_build_user_generation_prompt)
│   └── 回退生成 (_generate_fallback_input)
├── 对话模拟
│   ├── 完整对话 (simulate_conversation)
│   ├── 结束判断 (_should_end_conversation)
│   └── 上下文管理
└── 测试分析
    ├── 批量生成 (generate_test_scenarios)
    ├── 结果分析 (analyze_test_results)
    ├── 报告生成 (print_analysis_report)
    └── 数据保存 (save_test_results)
```

## 📁 文件结构

```
taxi_demo/
├── user_behavior_simulator.py      # 核心模拟器类
├── demo_user_simulator.py          # 交互式演示脚本
├── test_user_simulator.py          # 测试验证脚本
├── example_with_api_keys.py        # 使用示例（含API配置）
├── setup_environment.sh            # 环境配置脚本
├── README_用户行为模拟器.md        # 详细使用文档
└── 用户行为模拟器总结.md           # 项目总结（本文件）
```

## 🚀 快速开始

### 1. 环境配置

```bash
# 设置API密钥
export BAILIAN_API_KEY="your_bailian_api_key"
export AMAP_API_KEY="your_amap_api_key"

# 安装依赖
pip install openai
```

### 2. 运行测试

```bash
# 系统测试
python test_user_simulator.py

# 快速演示
python test_user_simulator.py --demo

# 完整演示
python demo_user_simulator.py
```

### 3. 编程使用

```python
from user_behavior_simulator import UserBehaviorSimulator

# 初始化
simulator = UserBehaviorSimulator()

# 创建会话
session_id = simulator.generate_session_id()
simulator.assign_scenario_to_session(session_id, 1)

# 生成用户输入
user_input = simulator.generate_user_input(session_id, "", 1)

# 模拟完整对话
conversation = simulator.simulate_conversation(session_id)
```

## 📊 测试结果示例

### 输出格式

```json
{
  "session_id": "uuid",
  "scenario_type": 1,
  "scenario_name": "首次使用用户",
  "conversation": [
    {
      "turn": 1,
      "user_input": "你好，我想打车",
      "system_response": "您好！我可以帮您叫车...",
      "timestamp": "2024-01-01T12:00:00"
    }
  ],
  "summary": {
    "total_turns": 3,
    "is_successful": true,
    "conversation_length": 150
  }
}
```

### 分析报告

```
整体统计:
  总场景数: 10
  成功场景数: 8
  成功率: 80.0%
  平均轮次: 3.2

按场景类型统计:
  1. 首次使用用户:
    成功率: 75.0%
    平均轮次: 3.5
```

## 🔧 技术特点

### 1. 大模型集成

- **API调用**: 使用百炼OpenAI兼容接口
- **提示工程**: 针对不同场景定制提示词
- **联网搜索**: 支持enable_search获取实时信息
- **错误处理**: 完善的异常处理和回退机制

### 2. 会话管理

- **UUID标识**: 每个会话有唯一标识符
- **场景绑定**: session_id与用户场景一一对应
- **历史记录**: 完整保存对话历史和元数据
- **上下文传递**: 支持多轮对话上下文

### 3. 数据分析

- **成功率计算**: 基于系统回复关键词判断
- **统计分组**: 按场景类型分别统计
- **JSON导出**: 结构化数据便于后续分析
- **可视化报告**: 清晰的文本报告格式

## 🎯 应用场景

### 1. 系统测试

- **功能验证**: 测试打车系统各项功能
- **边界测试**: 验证系统对异常输入的处理
- **性能评估**: 统计响应时间和成功率
- **用户体验**: 评估不同用户类型的满意度

### 2. 开发调试

- **Function Calling**: 测试函数调用准确性
- **参数补全**: 验证缺失参数的处理
- **错误恢复**: 测试系统错误处理能力
- **对话流程**: 优化多轮对话逻辑

### 3. 产品优化

- **用户画像**: 了解不同用户的行为模式
- **交互设计**: 优化对话界面和流程
- **功能规划**: 基于用户需求规划新功能
- **质量保证**: 持续监控系统表现

## 🔮 扩展方向

### 1. 更多用户类型

- 商务用户（对时间敏感）
- 老年用户（需要更多帮助）
- 国际用户（语言障碍）
- VIP用户（特殊需求）

### 2. 高级功能

- 情感分析（用户满意度）
- 意图识别（更精确的场景分类）
- 个性化（基于历史行为调整）
- 多语言支持（国际化测试）

### 3. 集成扩展

- 其他对话系统（不限于打车）
- 自动化测试流水线
- 性能监控仪表板
- A/B测试框架

## ✅ 项目成果

1. **完整的用户行为模拟系统** - 支持5种用户场景
2. **智能对话生成** - 基于大模型的自然语言生成
3. **自动化测试框架** - 批量测试和结果分析
4. **详细的使用文档** - 包含示例和最佳实践
5. **可扩展的架构** - 易于添加新场景和功能

## 📝 使用建议

1. **API配置**: 确保正确设置百炼API密钥
2. **小规模测试**: 先进行小规模测试验证功能
3. **成本控制**: 注意API调用费用，合理设置测试规模
4. **结果分析**: 重视测试结果，用于系统优化
5. **持续改进**: 根据实际使用情况调整用户场景

这个用户行为模拟智能体为测试和优化taxi_agent_system提供了强大的工具，能够全面评估系统在不同用户场景下的表现，为产品改进提供数据支持。
