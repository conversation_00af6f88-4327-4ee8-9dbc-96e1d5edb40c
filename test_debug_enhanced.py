#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强调试功能测试
测试Function执行前后的调试分析和简洁回复优化
"""

import json
import uuid
import time
from taxi_agent_system import EnhancedTaxiAgent


def generate_session_id():
    """生成随机session_id"""
    return f"session_{uuid.uuid4().hex[:8]}"


def test_function_debug_scenarios():
    """测试各种Function调试场景"""
    print("=== Function调试场景测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试场景
    test_scenarios = [
        {
            "name": "1. 功能解析错误 (score: -1)",
            "function_name": "unknown_function",
            "args": {"test": "value"},
            "expected_score": -1
        },
        {
            "name": "2. 参数缺失 (score: 0)",
            "function_name": "call_taxi_service",
            "args": {"start_place": "康德大厦"},  # 缺少end_place
            "expected_score": 0
        },
        {
            "name": "3. 参数不合理 (score: 0.5)",
            "function_name": "call_taxi_service",
            "args": {"start_place": "火星", "end_place": "金星"},
            "expected_score": 0.5
        },
        {
            "name": "4. 正常调用 (score: 1.0)",
            "function_name": "call_taxi_service",
            "args": {"start_place": "康德大厦", "end_place": "太阳宫", "car_prefer": "舒适型"},
            "expected_score": 1.0
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n--- {scenario['name']} ---")
        print(f"函数: {scenario['function_name']}")
        print(f"参数: {json.dumps(scenario['args'], ensure_ascii=False)}")
        
        # 执行前调试
        debug_result = agent._debug_function_call_pre(
            scenario['function_name'],
            scenario['args'],
            session_id
        )
        
        print(f"调试分数: {debug_result['debug_score']} (预期: {scenario['expected_score']})")
        print(f"是否执行: {debug_result['should_execute']}")
        
        if debug_result['issues']:
            print(f"发现问题: {debug_result['issues']}")
        
        if debug_result['fallback_response']:
            print(f"备用回复: {debug_result['fallback_response']['error']}")
        
        # 如果应该执行，则执行并进行后调试
        if debug_result['should_execute']:
            try:
                function_response = agent._execute_function(
                    scenario['function_name'],
                    scenario['args'],
                    session_id
                )
                
                post_debug = agent._debug_function_call_post(
                    scenario['function_name'],
                    scenario['args'],
                    function_response,
                    session_id
                )
                
                print(f"执行后调试分数: {post_debug['debug_score']}")
                if post_debug['issues']:
                    print(f"执行后问题: {post_debug['issues']}")
                    
            except Exception as e:
                print(f"执行异常: {e}")
        
        print("-" * 50)
    
    return agent


def test_response_brevity_optimization():
    """测试回复简洁性优化"""
    print("\n=== 回复简洁性优化测试 ===")
    
    agent = EnhancedTaxiAgent()
    
    # 测试冗长回复的优化
    verbose_responses = [
        "非常感谢您的询问，如果您还有其他需要帮助的地方，请随时告诉我，我很乐意为您提供服务。",
        "为了更好地为您提供帮助，请您提供更详细的信息。如果您需要打车服务，我可以帮助您安排。",
        "我可以帮助您查询地点信息。我可以帮助您查询地点信息。请告诉我您的需求。",  # 重复句子
    ]
    
    for i, verbose in enumerate(verbose_responses, 1):
        print(f"\n--- 优化测试 {i} ---")
        print(f"原始回复: {verbose}")
        
        optimized = agent._optimize_response_brevity(verbose)
        print(f"优化后: {optimized}")
        
        # 计算简化程度
        reduction = (len(verbose) - len(optimized)) / len(verbose) * 100
        print(f"简化程度: {reduction:.1f}%")


def test_real_conversation_with_debug():
    """测试真实对话中的调试功能"""
    print("\n=== 真实对话调试测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟各种用户输入
    test_conversations = [
        "我要从火星打车到金星",  # 不合理参数
        "帮我叫车",  # 参数缺失
        "我要从康德大厦打车到太阳宫",  # 正常请求
        "查询一下三星集团2024年的盈利情况",  # 超出功能范围
    ]
    
    for i, message in enumerate(test_conversations, 1):
        print(f"\n--- 对话 {i}: {message} ---")
        
        try:
            start_time = time.time()
            response = agent.process_message(message, session_id)
            end_time = time.time()
            
            print(f"回复: {response}")
            print(f"响应时间: {end_time - start_time:.2f}s")
            
        except Exception as e:
            print(f"对话失败: {e}")
        
        time.sleep(1)
    
    # 显示调试摘要
    print(f"\n📊 Function调试摘要:")
    debug_summary = agent.debug_agent.get_function_debug_summary()
    for key, value in debug_summary.items():
        print(f"  {key}: {value}")
    
    return agent


def test_intelligent_assistance():
    """测试智能辅助功能"""
    print("\n=== 智能辅助功能测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 测试超出范围的请求
    out_of_scope_requests = [
        "帮我查下三星集团24年的盈利情况",
        "今天北京的天气怎么样？",
        "推荐一些好看的电影",
        "帮我订一张机票"
    ]
    
    for request in out_of_scope_requests:
        print(f"\n用户: {request}")
        
        # 模拟智能分析
        analysis = agent._analyze_out_of_scope_request(request)
        print(f"分析结果: {analysis}")
        
        # 提供建议
        suggestions = agent._provide_intelligent_suggestions(request)
        print(f"建议: {suggestions}")


def test_error_recovery():
    """测试错误恢复机制"""
    print("\n=== 错误恢复机制测试 ===")
    
    agent = EnhancedTaxiAgent()
    session_id = generate_session_id()
    
    # 模拟各种错误场景
    error_scenarios = [
        {
            "function": "call_taxi_service",
            "args": {"start_place": "", "end_place": "太阳宫"},
            "error_type": "empty_parameter"
        },
        {
            "function": "mcp_geocode_address", 
            "args": {"address": "不存在的地方12345"},
            "error_type": "invalid_location"
        }
    ]
    
    for scenario in error_scenarios:
        print(f"\n--- 错误场景: {scenario['error_type']} ---")
        
        try:
            result = agent._execute_function(
                scenario['function'],
                scenario['args'],
                session_id
            )
            
            if not result.get("status", False):
                # 分析错误并提供恢复建议
                recovery_suggestions = agent._analyze_failure_and_suggest(
                    scenario['function'],
                    scenario['args'],
                    result.get("error", "")
                )
                
                print(f"错误: {result.get('error', '未知错误')}")
                print(f"恢复建议: {recovery_suggestions}")
            
        except Exception as e:
            print(f"执行异常: {e}")


def main():
    """主测试函数"""
    print("增强调试功能测试")
    print("=" * 60)
    
    # 1. 测试Function调试场景
    agent1 = test_function_debug_scenarios()
    
    # 2. 测试回复简洁性优化
    test_response_brevity_optimization()
    
    # 3. 测试真实对话中的调试
    agent2 = test_real_conversation_with_debug()
    
    # 4. 测试智能辅助功能
    test_intelligent_assistance()
    
    # 5. 测试错误恢复机制
    test_error_recovery()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    print("✅ 完成的功能测试:")
    print("1. Function执行前后调试分析")
    print("2. 调试分数评估 (-1, 0, 0.5, 1.0)")
    print("3. 回复简洁性优化")
    print("4. 智能错误分析和建议")
    print("5. 错误恢复机制")
    
    print(f"\n🎯 主要改进:")
    print("- Assistant回复更加简洁明了")
    print("- Function调用前后完整调试")
    print("- 智能错误分类和建议")
    print("- 超出范围请求的智能处理")


if __name__ == "__main__":
    main()
